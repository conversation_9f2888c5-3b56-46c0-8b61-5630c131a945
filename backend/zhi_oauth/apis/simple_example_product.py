from pydantic import BaseModel
from typing import List, Any, Dict
from ninja import Field, ModelSchema
from ninja_extra import http_get, http_post, route
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile
from zhi_common.zhi_response.base import ZhiResponse
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import (
    BaseResponseSchema,
    ListIDValueMappingSchema,
    ExportRequestSchema,
    ImportResultSchema,
    ImportPreviewSchema
)
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import (
    BaseModelService,
)
from zhi_oauth.models import ExampleProduct


class ExampleProductSchemaIn(ModelSchema):
    """产品创建/更新Schema"""
    class Meta:
        model = ExampleProduct
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class ExampleProductSchemaOut(ModelSchema):
    class Meta:
        model = ExampleProduct
        fields = '__all__'


@auto_crud_api(
    ExampleProduct,
    prefix="ExampleProductV2",
    tags=["示例产品V2"],
    schema_in=ExampleProductSchemaIn,
    schema_out=ExampleProductSchemaOut,
    exclude=[]  # 排除自动生成的，使用显式定义的
    )
class ExampleProductControllerAPI(BaseModelService):
    """示例产品服务 - 支持导入导出功能"""
    model = ExampleProduct
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    # ID映射配置
    mappings_settings = {
        "id_key": 'id',
        "id_value": "name",
        "is_enabled": True,
    }

    @api_route(http_get, "/custom_method1", response={200: BaseResponseSchema[List[ExampleProductSchemaOut]]})
    def user_custom_method(self, request, code: str):
        """
        自定义方法
        """
        model = getattr(self, 'model', None)
        items = list(model.objects.filter(code=code).values())
        return ZhiResponse(
            data=items,
            message="查询成功"
        )
