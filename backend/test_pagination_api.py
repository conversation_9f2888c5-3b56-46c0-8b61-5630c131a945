#!/usr/bin/env python3
"""
测试文件管理API分页功能的脚本
"""
import requests
import json

def test_pagination_apis():
    """测试各个文件管理API的分页功能"""
    
    # API配置
    base_url = "http://localhost:8000"
    token = "access-8f137cefc3fc034f3f25215ac7c9b8935a45d5959bf600b468"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试的API端点
    apis_to_test = [
        {
            "name": "增强文件管理API - list_files",
            "url": f"{base_url}/api/enhanced-files/list",
            "params": {
                "page": 1,
                "page_size": 5,
                "pagination": True
            }
        }
    ]
    
    print("🚀 开始测试文件管理API分页功能...")
    print("=" * 60)
    
    for api in apis_to_test:
        print(f"\n📋 测试: {api['name']}")
        print(f"🔗 URL: {api['url']}")
        
        try:
            response = requests.get(api['url'], headers=headers, params=api['params'])
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 请求成功")
                
                # 检查响应格式
                print(f"📄 响应结构:")
                print(f"  - code: {data.get('code')}")
                print(f"  - success: {data.get('success')}")
                print(f"  - message: {data.get('message')}")
                
                response_data = data.get('data')
                if response_data:
                    if isinstance(response_data, dict):
                        # 检查是否是统一的分页格式
                        if 'items' in response_data:
                            print(f"  ✅ 统一分页格式:")
                            print(f"    - items: {len(response_data.get('items', []))} 条记录")
                            print(f"    - total: {response_data.get('total')}")
                            print(f"    - page: {response_data.get('page')}")
                            print(f"    - page_size: {response_data.get('page_size')}")
                            print(f"    - pages: {response_data.get('pages')}")
                        else:
                            print(f"  ❌ 非标准分页格式，data字段包含: {list(response_data.keys())}")
                    elif isinstance(response_data, list):
                        print(f"  ❌ 返回的是列表格式，不是分页格式，共 {len(response_data)} 条记录")
                    else:
                        print(f"  ❌ 未知的数据格式: {type(response_data)}")
                else:
                    print(f"  ❌ 没有data字段")
                    
            else:
                print(f"❌ 请求失败")
                print(f"📄 响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        print("-" * 40)
    
    print("\n🎯 测试完成！")
    print("\n📝 期望的统一分页格式:")
    print("""
    {
        "code": 2000,
        "success": true,
        "message": "查询成功",
        "data": {
            "items": [...],      // 数据列表
            "total": 100,        // 总记录数
            "page": 1,           // 当前页码
            "page_size": 10,     // 每页大小
            "pages": 10          // 总页数
        },
        "trace_id": "...",
        "timestamp": "..."
    }
    """)

if __name__ == "__main__":
    test_pagination_apis()
