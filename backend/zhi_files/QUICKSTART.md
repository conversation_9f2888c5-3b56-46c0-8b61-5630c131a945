# ZhiFiles 快速开始指南

## 🚀 5分钟快速上手

### 1. 启动服务
```bash
cd backend
python zhi_scripts/start_files.py
```

### 2. 访问服务
- 🌐 服务地址: http://127.0.0.1:8000/
- 📖 API文档: http://127.0.0.1:8000/api/docs/
- 📊 管理后台: http://127.0.0.1:8000/admin/

### 3. 测试API
```bash
# 运行API测试
python test_enhanced_api.py
```

## 📝 基础使用

### 文件上传
```bash
curl -X POST http://127.0.0.1:8000/api/simple/files/upload/ \
  -F "file=@example.jpg" \
  -F "description=测试图片"
```

### 文件列表
```bash
curl "http://127.0.0.1:8000/api/simple/files/list/"
```

### 文件下载
```bash
curl "http://127.0.0.1:8000/api/simple/files/download/{file_id}/" -O
```

## 🛠️ 管理命令

### 文件统计
```bash
python manage.py file_stats --settings=application.settings.zhi_files
```

### 文件清理
```bash
python manage.py cleanup_files --expired --dry-run --settings=application.settings.zhi_files
```

## ❓ 常见问题

### Q: 服务启动失败？
A: 检查依赖包安装：`pip install -r requirements.txt`

### Q: 文件上传失败？
A: 检查文件大小（默认限制100MB）和文件类型

### Q: API需要认证？
A: 开发环境下可以临时禁用认证，生产环境需要配置OAuth2令牌

## 📚 更多信息

详细文档请查看 [README.md](README.md)
