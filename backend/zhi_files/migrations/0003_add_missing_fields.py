# Generated manually to add missing fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhi_files', '0002_fix_primary_key'),
    ]

    operations = [
        # 添加缺失的字段
        migrations.AddField(
            model_name='filestorage',
            name='modifier_id',
            field=models.CharField(
                max_length=63, null=True, blank=True, db_index=True,
                verbose_name="修改人ID", help_text="修改人用户ID", db_comment='修改人用户ID'
            ),
        ),
        migrations.AddField(
            model_name='filestorage',
            name='modifier_name',
            field=models.CharField(
                max_length=255, null=True, blank=True,
                verbose_name="修改人姓名", help_text="修改人姓名", db_comment='修改人姓名'
            ),
        ),
    ]
