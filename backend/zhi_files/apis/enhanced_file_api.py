"""
增强文件管理API - 支持更多高级功能
"""

import os
import mimetypes
from typing import List, Optional, Dict, Any
from datetime import timedelta
from pathlib import Path

from django.http import HttpResponse, Http404, FileResponse
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from django.db import models
from django.utils import timezone
from django.conf import settings

from ninja import Query, File, Form, UploadedFile
from ninja_extra import api_controller, http_get, http_post, http_delete, http_put
from ninja_extra.controllers import ControllerBase
from ninja_extra.permissions import IsAuthenticated
from ninja_extra.pagination import paginate

from pydantic import BaseModel, Field

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema, PaginatedResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
from zhi_common.zhi_tools.req_util import get_client_ip, get_user_agent
from zhi_common.zhi_logger.core_logger import zhi_logger


# 延迟导入模型以避免循环导入
def get_file_models():
    from zhi_files.models import FileStorage, FileAccessLog
    return FileStorage, FileAccessLog


# Schema定义
class FileUploadSchema(BaseModel):
    """文件上传Schema"""
    description: Optional[str] = Field(None, description="文件描述")
    tags: Optional[List[str]] = Field(default_factory=list, description="文件标签")
    is_public: bool = Field(False, description="是否公开")
    expires_hours: Optional[int] = Field(None, description="过期时间(小时)")
    category: Optional[str] = Field(None, description="文件分类")


class FileInfoSchema(BaseModel):
    """文件信息Schema"""
    file_id: str
    original_name: str
    file_name: str
    file_path: str
    file_url: str
    file_size: int
    file_size_human: str
    file_type: str
    content_type: str
    file_extension: str
    file_hash: str
    status: str
    tags: List[str]
    description: str
    download_count: int
    last_download_at: Optional[str]
    expires_at: Optional[str]
    created_at: str
    creator_name: Optional[str]


class FileListFilterSchema(BaseModel):
    """文件列表过滤Schema"""
    file_type: Optional[str] = Field(None, description="文件类型")
    status: Optional[str] = Field(None, description="文件状态")
    tag: Optional[str] = Field(None, description="标签过滤")
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    creator_id: Optional[str] = Field(None, description="创建者ID")
    page: int = Field(1, description="页码")
    page_size: int = Field(10, description="每页数量")
    pagination: bool = Field(True, description="是否分页")


class FileBatchOperationSchema(BaseModel):
    """批量操作Schema"""
    file_ids: List[str] = Field(..., description="文件ID列表")
    operation: str = Field(..., description="操作类型: delete, update_tags, update_status")
    data: Optional[Dict[str, Any]] = Field(None, description="操作数据")


@api_controller('/enhanced-files', tags=['增强文件管理'])
class EnhancedFileAPIController(ControllerBase):
    """增强文件管理API控制器"""
    
    def __init__(self):
        """初始化文件管理器"""
        # 默认配置
        self.default_config = FileUploadConfig(
            max_file_size=100 * 1024 * 1024,  # 100MB
            allowed_types=['image', 'document', 'archive', 'video', 'audio'],
            upload_path='zhi_files_uploads',
            organize_by_date=True,
            generate_unique_name=True
        )
        self.file_manager = FileManager(self.default_config)
    
    @staticmethod
    def _log_file_access(file_record, action: str, request, extra_data: dict = None):
        """记录文件访问日志"""
        try:
            file_storage, file_access_log = get_file_models()
            
            log_data = {
                'file': file_record,
                'action': action,
                'ip_address': get_client_ip(request),
                'user_agent': get_user_agent(request),
                'referer': request.META.get('HTTP_REFERER'),
                'extra_data': extra_data or {}
            }
            
            file_access_log.objects.create(**log_data)
            
        except Exception as e:
            zhi_logger.warning(f"记录文件访问日志失败: {e}")

    @staticmethod
    def _get_file_type_from_extension(extension: str) -> str:
        """根据文件扩展名获取文件类型"""
        extension = extension.lower()

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        document_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
        video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm']
        audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma']

        if extension in image_extensions:
            return 'image'
        elif extension in document_extensions:
            return 'document'
        elif extension in archive_extensions:
            return 'archive'
        elif extension in video_extensions:
            return 'video'
        elif extension in audio_extensions:
            return 'audio'
        else:
            return 'other'

    @api_route(http_post, "/upload", response={200: BaseResponseSchema[FileInfoSchema]})
    def upload_file(
        self, 
        request,
        file: UploadedFile = File(...),
        upload_info: FileUploadSchema = Form(...)
    ):
        """
        上传文件（增强版）
        支持文件描述、标签、过期时间等高级功能
        """
        try:
            # 上传文件
            upload_result = self.file_manager.upload_file(file)
            
            # 获取模型类
            file_storage, file_access_log = get_file_models()
            
            # 计算过期时间
            expires_at = None
            if upload_info.expires_hours:
                expires_at = timezone.now() + timedelta(hours=upload_info.expires_hours)
            
            # 从文件名获取扩展名和类型
            file_extension = os.path.splitext(upload_result.filename)[1].lower()
            file_type = self._get_file_type_from_extension(file_extension)

            # 创建文件记录
            file_record = file_storage.objects.create(
                file_id=upload_result.file_id,
                original_name=upload_result.filename,
                file_name=os.path.basename(upload_result.file_path),
                file_path=upload_result.file_path,
                file_url=upload_result.file_url,
                file_size=upload_result.file_size,
                file_type=file_type,
                content_type=upload_result.content_type,
                file_extension=file_extension,
                file_hash=upload_result.file_hash,
                upload_ip=get_client_ip(request),
                tags=upload_info.tags or [],
                description=upload_info.description or '',
                expires_at=expires_at,
                metadata={
                    'is_public': upload_info.is_public,
                    'category': upload_info.category,
                    'user_agent': get_user_agent(request)
                }
            )
            
            # 记录访问日志
            self._log_file_access(file_record, 'upload', request, {
                'file_size': upload_result.file_size,
                'file_type': upload_result.file_type
            })
            
            # 构建响应数据
            response_data = FileInfoSchema(
                file_id=file_record.file_id,
                original_name=file_record.original_name,
                file_name=file_record.file_name,
                file_path=file_record.file_path,
                file_url=file_record.file_url,
                file_size=file_record.file_size,
                file_size_human=file_record.file_size_human,
                file_type=file_record.file_type,
                content_type=file_record.content_type,
                file_extension=file_record.file_extension,
                file_hash=file_record.file_hash,
                status=file_record.status,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                created_at=file_record.created_at.isoformat(),
                creator_name=file_record.creator_name
            )
            
            return create_response(
                data=response_data,
                message="文件上传成功"
            )
            
        except Exception as e:
            zhi_logger.error(f"文件上传失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"文件上传失败: {str(e)}",
                success=False
            )
    
    @api_route(http_get, "/list", response={200: BaseResponseSchema})
    def list_files(self, request, filters: FileListFilterSchema = Query(...)):
        """
        获取文件列表（增强版）
        支持更多过滤条件和搜索功能，返回统一分页格式
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 构建查询
            queryset = file_storage.objects.filter(is_deleted=False)

            # 应用过滤条件
            if filters.file_type:
                queryset = queryset.filter(file_type=filters.file_type)

            if filters.status:
                queryset = queryset.filter(status=filters.status)

            if filters.tag:
                queryset = queryset.filter(tags__contains=[filters.tag])

            if filters.category:
                queryset = queryset.filter(metadata__category=filters.category)

            if filters.creator_id:
                queryset = queryset.filter(creator_id=filters.creator_id)

            if filters.search:
                queryset = queryset.filter(
                    models.Q(original_name__icontains=filters.search) |
                    models.Q(description__icontains=filters.search) |
                    models.Q(tags__contains=[filters.search])
                )

            # 排序
            queryset = queryset.order_by('-created_at')

            # 序列化数据的辅助函数
            def serialize_file(file_obj):
                return {
                    'file_id': file_obj.file_id,
                    'original_name': file_obj.original_name,
                    'file_name': file_obj.file_name,
                    'file_path': file_obj.file_path,
                    'file_url': file_obj.file_url,
                    'file_size': file_obj.file_size,
                    'file_size_human': file_obj.file_size_human,
                    'file_type': file_obj.file_type,
                    'content_type': file_obj.content_type,
                    'file_extension': file_obj.file_extension,
                    'file_hash': file_obj.file_hash,
                    'status': file_obj.status,
                    'tags': file_obj.tags or [],
                    'description': file_obj.description or '',
                    'download_count': file_obj.download_count,
                    'last_download_at': file_obj.last_download_at.isoformat() if file_obj.last_download_at else None,
                    'expires_at': file_obj.expires_at.isoformat() if file_obj.expires_at else None,
                    'created_at': file_obj.created_at.isoformat(),
                    'creator_name': file_obj.creator_name or ''
                }

            # 处理分页
            if filters.pagination:
                paginator = Paginator(queryset, filters.page_size)
                page_obj = paginator.get_page(filters.page)

                return create_response(
                    data={
                        "items": [serialize_file(file_obj) for file_obj in page_obj.object_list],
                        "total": paginator.count,
                        "page": page_obj.number,
                        "page_size": filters.page_size,
                        "pages": paginator.num_pages
                    },
                    message="查询成功"
                )
            else:
                # 不分页，返回所有数据
                return create_response(
                    data=[serialize_file(file_obj) for file_obj in queryset],
                    message="查询成功"
                )

        except Exception as e:
            zhi_logger.error(f"获取文件列表失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"获取文件列表失败: {str(e)}",
                success=False
            )

    @api_route(http_get, "/download/{file_id}", response={200: None})
    def download_file(self, request, file_id: str):
        """
        下载文件（增强版）
        支持权限检查和访问日志
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 获取文件记录
            try:
                file_record = file_storage.objects.get(file_id=file_id, is_deleted=False)
            except file_storage.DoesNotExist:
                raise Http404("文件不存在")

            # 检查文件权限
            current_user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
            if not file_record.can_access(current_user_id):
                return create_response(
                    data=None,
                    code=ResponseCode.FORBIDDEN,
                    message="无权限访问此文件",
                    success=False
                )

            # 检查文件是否存在
            if not default_storage.exists(file_record.file_path):
                return create_response(
                    data=None,
                    code=ResponseCode.NOT_FOUND,
                    message="文件不存在",
                    success=False
                )

            # 更新下载统计
            file_record.increment_download_count()

            # 记录访问日志
            self._log_file_access(file_record, 'download', request)

            # 返回文件响应
            file_path = default_storage.path(file_record.file_path)
            response = FileResponse(
                open(file_path, 'rb'),
                content_type=file_record.content_type,
                as_attachment=True,
                filename=file_record.original_name
            )

            return response

        except Http404:
            raise
        except Exception as e:
            zhi_logger.error(f"文件下载失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"文件下载失败: {str(e)}",
                success=False
            )

    @api_route(http_get, "/info/{file_id}", response={200: BaseResponseSchema[FileInfoSchema]})
    def get_file_info(self, request, file_id: str):
        """
        获取文件详细信息
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 获取文件记录
            try:
                file_record = file_storage.objects.get(file_id=file_id, is_deleted=False)
            except file_storage.DoesNotExist:
                return create_response(
                    data=None,
                    code=ResponseCode.NOT_FOUND,
                    message="文件不存在",
                    success=False
                )

            # 记录访问日志
            self._log_file_access(file_record, 'view', request)

            # 构建响应数据
            response_data = FileInfoSchema(
                file_id=file_record.file_id,
                original_name=file_record.original_name,
                file_name=file_record.file_name,
                file_path=file_record.file_path,
                file_url=file_record.file_url,
                file_size=file_record.file_size,
                file_size_human=file_record.file_size_human,
                file_type=file_record.file_type,
                content_type=file_record.content_type,
                file_extension=file_record.file_extension,
                file_hash=file_record.file_hash,
                status=file_record.status,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                created_at=file_record.created_at.isoformat(),
                creator_name=file_record.creator_name
            )

            return create_response(
                data=response_data,
                message="获取文件信息成功"
            )

        except Exception as e:
            zhi_logger.error(f"获取文件信息失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"获取文件信息失败: {str(e)}",
                success=False
            )

    @api_route(http_put, "/update/{file_id}", response={200: BaseResponseSchema[FileInfoSchema]})
    def update_file_info(self, request, file_id: str, update_data: FileUploadSchema):
        """
        更新文件信息
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 获取文件记录
            try:
                file_record = file_storage.objects.get(file_id=file_id, is_deleted=False)
            except file_storage.DoesNotExist:
                return create_response(
                    data=None,
                    code=ResponseCode.NOT_FOUND,
                    message="文件不存在",
                    success=False
                )

            # 更新文件信息
            if update_data.description is not None:
                file_record.description = update_data.description

            if update_data.tags is not None:
                file_record.tags = update_data.tags

            if update_data.expires_hours is not None:
                file_record.expires_at = timezone.now() + timedelta(hours=update_data.expires_hours)

            # 更新元数据
            if update_data.is_public is not None:
                file_record.set_metadata('is_public', update_data.is_public)

            if update_data.category is not None:
                file_record.set_metadata('category', update_data.category)

            file_record.save()

            # 记录访问日志
            self._log_file_access(file_record, 'update', request, {
                'updated_fields': ['description', 'tags', 'expires_at', 'metadata']
            })

            # 构建响应数据
            response_data = FileInfoSchema(
                file_id=file_record.file_id,
                original_name=file_record.original_name,
                file_name=file_record.file_name,
                file_path=file_record.file_path,
                file_url=file_record.file_url,
                file_size=file_record.file_size,
                file_size_human=file_record.file_size_human,
                file_type=file_record.file_type,
                content_type=file_record.content_type,
                file_extension=file_record.file_extension,
                file_hash=file_record.file_hash,
                status=file_record.status,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                created_at=file_record.created_at.isoformat(),
                creator_name=file_record.creator_name
            )

            return create_response(
                data=response_data,
                message="文件信息更新成功"
            )

        except Exception as e:
            zhi_logger.error(f"更新文件信息失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"更新文件信息失败: {str(e)}",
                success=False
            )

    @api_route(http_delete, "/delete/{file_id}", response={200: BaseResponseSchema[None]})
    def delete_file(self, request, file_id: str, force_delete: bool = False):
        """
        删除文件（软删除或硬删除）
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 获取文件记录
            try:
                file_record = file_storage.objects.get(file_id=file_id, is_deleted=False)
            except file_storage.DoesNotExist:
                return create_response(
                    data=None,
                    code=ResponseCode.NOT_FOUND,
                    message="文件不存在",
                    success=False
                )

            if force_delete:
                # 硬删除：删除物理文件和数据库记录
                try:
                    if default_storage.exists(file_record.file_path):
                        default_storage.delete(file_record.file_path)
                except Exception as e:
                    zhi_logger.warning(f"删除物理文件失败: {e}")

                # 记录访问日志
                self._log_file_access(file_record, 'delete', request, {'force_delete': True})

                # 删除数据库记录
                file_record.delete(force_delete=True)
                message = "文件已永久删除"
            else:
                # 软删除：只标记为已删除
                file_record.mark_as_deleted()

                # 记录访问日志
                self._log_file_access(file_record, 'delete', request, {'force_delete': False})
                message = "文件已删除"

            return create_response(
                data=None,
                message=message
            )

        except Exception as e:
            zhi_logger.error(f"删除文件失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"删除文件失败: {str(e)}",
                success=False
            )

    @api_route(http_post, "/batch", response={200: BaseResponseSchema[Dict[str, Any]]})
    def batch_operation(self, request, operation_data: FileBatchOperationSchema):
        """
        批量操作文件
        """
        try:
            # 获取模型类
            file_storage, file_access_log = get_file_models()

            # 获取文件记录
            files = file_storage.objects.filter(
                file_id__in=operation_data.file_ids,
                is_deleted=False
            )

            if not files.exists():
                return create_response(
                    data=None,
                    code=ResponseCode.NOT_FOUND,
                    message="未找到指定的文件",
                    success=False
                )

            results = {
                'success_count': 0,
                'failed_count': 0,
                'results': []
            }

            for file_record in files:
                try:
                    if operation_data.operation == 'delete':
                        file_record.mark_as_deleted()
                        self._log_file_access(file_record, 'delete', request, {'batch_operation': True})
                        results['results'].append({
                            'file_id': file_record.file_id,
                            'status': 'success',
                            'message': '删除成功'
                        })
                        results['success_count'] += 1

                    elif operation_data.operation == 'update_tags':
                        new_tags = operation_data.data.get('tags', [])
                        file_record.tags = new_tags
                        file_record.save()
                        self._log_file_access(file_record, 'update', request, {
                            'batch_operation': True,
                            'updated_tags': new_tags
                        })
                        results['results'].append({
                            'file_id': file_record.file_id,
                            'status': 'success',
                            'message': '标签更新成功'
                        })
                        results['success_count'] += 1

                    elif operation_data.operation == 'update_status':
                        new_status = operation_data.data.get('status')
                        if new_status in ['active', 'deleted', 'expired']:
                            file_record.status = new_status
                            file_record.save()
                            self._log_file_access(file_record, 'update', request, {
                                'batch_operation': True,
                                'updated_status': new_status
                            })
                            results['results'].append({
                                'file_id': file_record.file_id,
                                'status': 'success',
                                'message': '状态更新成功'
                            })
                            results['success_count'] += 1
                        else:
                            results['results'].append({
                                'file_id': file_record.file_id,
                                'status': 'failed',
                                'message': '无效的状态值'
                            })
                            results['failed_count'] += 1
                    else:
                        results['results'].append({
                            'file_id': file_record.file_id,
                            'status': 'failed',
                            'message': '不支持的操作类型'
                        })
                        results['failed_count'] += 1

                except Exception as e:
                    results['results'].append({
                        'file_id': file_record.file_id,
                        'status': 'failed',
                        'message': str(e)
                    })
                    results['failed_count'] += 1

            return create_response(
                data=results,
                message=f"批量操作完成，成功: {results['success_count']}, 失败: {results['failed_count']}"
            )

        except Exception as e:
            zhi_logger.error(f"批量操作失败: {e}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_SERVER_ERROR,
                message=f"批量操作失败: {str(e)}",
                success=False
            )
