"""
ZhiFiles API 配置
"""
from ninja_extra import NinjaExtraAPI

# 尝试导入增强API，如果失败则使用基础API
try:
    from .apis.enhanced_file_api import EnhancedFileAPIController
    ENHANCED_API_AVAILABLE = True
except ImportError as e:
    print(f"增强API导入失败，将使用基础API: {e}")
    ENHANCED_API_AVAILABLE = False

try:
    from .apis.file_management import FileStorageControllerAPI
    BASIC_API_AVAILABLE = True
except ImportError as e:
    print(f"基础API导入失败: {e}")
    BASIC_API_AVAILABLE = False

# 尝试导入认证和异常处理
try:
    from zhi_common.zhi_auth.core_auth import GlobalOAuth2
    from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers
    AUTH_AVAILABLE = True
except ImportError:
    print("认证系统不可用，将使用无认证模式")
    AUTH_AVAILABLE = False

# 创建 API 实例
api_config = {
    "title": "ZhiFiles API",
    "version": "2.0.0",
    "description": "ZhiAdmin 文件管理系统 API - 增强版",
    "docs_url": "zhi_files/docs",
    "openapi_url": "openapi.json",
    "openapi_extra": {
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
}

if AUTH_AVAILABLE:
    api_config["auth"] = GlobalOAuth2

api = NinjaExtraAPI(**api_config)

# 注册异常处理器
if AUTH_AVAILABLE:
    try:
        register_exception_handlers(api)
    except Exception as e:
        print(f"异常处理器注册失败: {e}")

# 注册控制器
controllers_registered = 0

if BASIC_API_AVAILABLE:
    try:
        api.register_controllers(FileStorageControllerAPI)
        controllers_registered += 1
        print("✅ 基础文件存储管理API已注册")
    except Exception as e:
        print(f"基础API注册失败: {e}")

if ENHANCED_API_AVAILABLE:
    try:
        api.register_controllers(EnhancedFileAPIController)
        controllers_registered += 1
        print("✅ 增强文件管理API已注册")
    except Exception as e:
        print(f"增强API注册失败: {e}")

if controllers_registered == 0:
    print("⚠️  没有成功注册任何API控制器")
else:
    print(f"✅ 成功注册 {controllers_registered} 个API控制器")
