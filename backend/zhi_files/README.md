# ZhiFiles 文件管理系统

## 📋 概述

ZhiFiles 是 ZhiAdmin 项目的文件管理子系统，提供完整的文件上传、下载、管理和统计功能。系统采用微服务架构设计，支持独立部署和扩展。

## 🚀 快速开始

### 启动服务

```bash
# 方式1: 使用启动脚本（推荐）
python zhi_scripts/start_files.py

# 方式2: 直接启动
python manage.py runserver --settings=application.settings.zhi_files
```

### 访问地址

- 🌐 **服务地址**: http://127.0.0.1:8000/
- 📖 **API文档**: http://127.0.0.1:8000/api/docs/
- 📊 **管理后台**: http://127.0.0.1:8000/admin/
- 🔗 **OpenAPI Schema**: http://127.0.0.1:8000/api/openapi.json

## 🏗️ 系统架构

### 核心组件

```
zhi_files/
├── apis/                    # API接口层
│   ├── enhanced_file_api.py    # 增强API控制器
│   ├── simple_file_api.py      # 简化API视图
│   └── file_management.py      # 基础API
├── management/commands/     # 管理命令
│   ├── cleanup_files.py        # 文件清理
│   └── file_stats.py           # 统计报告
├── models.py               # 数据模型
├── api.py                  # API配置
├── database_router.py      # 数据库路由
└── apps.py                 # 应用配置
```

### 数据模型

#### FileStorage - 文件存储模型
```python
class FileStorage(CoreModel):
    file_id = CharField(max_length=64, unique=True)      # 文件唯一ID
    original_name = CharField(max_length=255)            # 原始文件名
    file_path = CharField(max_length=500)                # 存储路径
    file_url = URLField(max_length=500)                  # 访问URL
    file_size = BigIntegerField()                        # 文件大小
    file_type = CharField(max_length=20)                 # 文件类型
    content_type = CharField(max_length=100)             # MIME类型
    file_extension = CharField(max_length=10)            # 文件扩展名
    file_hash = CharField(max_length=64)                 # 文件哈希
    
    # 高级功能
    tags = JSONField(default=list)                       # 文件标签
    metadata = JSONField(default=dict)                   # 元数据
    description = TextField(blank=True)                  # 文件描述
    expires_at = DateTimeField(null=True, blank=True)    # 过期时间
    
    # 统计信息
    download_count = IntegerField(default=0)             # 下载次数
    last_download_at = DateTimeField(null=True)          # 最后下载时间
    
    # 审计字段（继承自CoreModel）
    # created_at, updated_at, creator_id, creator_name, is_deleted, deleted_at
```

#### FileAccessLog - 访问日志模型
```python
class FileAccessLog(CoreModel):
    file = ForeignKey(FileStorage)                       # 关联文件
    action = CharField(max_length=20)                    # 操作类型
    ip_address = GenericIPAddressField()                 # 客户端IP
    user_agent = TextField(blank=True)                   # 用户代理
    referer = URLField(blank=True)                       # 来源页面
    extra_data = JSONField(default=dict)                 # 额外数据
```

## 🔌 API接口

### 增强API（推荐）

基于 ninja-extra 的完整功能API，支持自动文档生成和类型验证。

#### 文件上传
```http
POST /api/enhanced-files/upload
Content-Type: multipart/form-data

file: <文件>
description: "文件描述"
tags: ["标签1", "标签2"]
is_public: false
expires_hours: 24
category: "分类"
```

#### 文件列表
```http
GET /api/enhanced-files/list?page=1&page_size=20&file_type=image&search=关键词
```

#### 文件下载
```http
GET /api/enhanced-files/download/{file_id}
```

#### 文件信息
```http
GET /api/enhanced-files/info/{file_id}
```

#### 更新文件信息
```http
PUT /api/enhanced-files/update/{file_id}
Content-Type: application/json

{
    "description": "新描述",
    "tags": ["新标签"],
    "expires_hours": 48
}
```

#### 批量操作
```http
POST /api/enhanced-files/batch
Content-Type: application/json

{
    "file_ids": ["id1", "id2"],
    "operation": "delete",
    "data": {}
}
```

### 简化API（兼容性）

基于 Django 视图的简化API，用于快速集成。

```http
POST /api/simple/files/upload/
GET  /api/simple/files/list/
GET  /api/simple/files/download/{file_id}/
```

## 🛠️ 管理工具

### 文件清理命令

```bash
# 清理过期文件（试运行）
python manage.py cleanup_files --expired --dry-run

# 清理30天前的已删除文件
python manage.py cleanup_files --deleted --days 30

# 清理孤儿文件（数据库中不存在的物理文件）
python manage.py cleanup_files --orphaned

# 强制清理，不询问确认
python manage.py cleanup_files --expired --force
```

### 文件统计命令

```bash
# 基础统计报告
python manage.py file_stats

# 详细统计报告
python manage.py file_stats --detailed

# 导出统计报告
python manage.py file_stats --export /path/to/report.txt

# 指定统计周期
python manage.py file_stats --days 7 --detailed
```

## ⚙️ 配置选项

### 基础配置

```python
# application/settings/zhi_files.py
ZHI_FILES_CONFIG = {
    # 文件上传配置
    'DEFAULT_MAX_FILE_SIZE': 100 * 1024 * 1024,  # 100MB
    'DEFAULT_ALLOWED_TYPES': ['image', 'document', 'archive', 'video', 'audio'],
    'DEFAULT_UPLOAD_PATH': 'zhi_files_uploads',
    'ORGANIZE_BY_DATE': True,
    'GENERATE_UNIQUE_NAME': True,
    
    # 安全配置
    'ENABLE_ACCESS_LOG': True,
    'ENABLE_DOWNLOAD_STATS': True,
    'ENABLE_FILE_EXPIRY': True,
    'ENABLE_PERMISSION_CHECK': True,
    
    # 清理配置
    'AUTO_CLEANUP_EXPIRED': True,
    'CLEANUP_INTERVAL_HOURS': 24,
    'KEEP_DELETED_FILES_DAYS': 30,
    
    # 缓存配置
    'ENABLE_FILE_CACHE': True,
    'CACHE_TIMEOUT': 3600,
    'CACHE_KEY_PREFIX': 'zhi_files',
    
    # 微服务配置
    'MICROSERVICE_MODE': False,
    'SERVICE_NAME': 'zhi_files',
    'SERVICE_VERSION': '2.0.0',
    
    # 性能配置
    'ENABLE_ASYNC_UPLOAD': True,
    'CHUNK_UPLOAD_SIZE': 5 * 1024 * 1024,  # 5MB
    'MAX_CONCURRENT_UPLOADS': 3,
    
    # 预览配置
    'ENABLE_FILE_PREVIEW': True,
    'PREVIEW_IMAGE_SIZES': [(150, 150), (300, 300), (800, 600)],
    'PREVIEW_CACHE_TIMEOUT': 7200,
}
```

### 微服务配置

```python
# 启用微服务模式
ZHI_FILES_CONFIG['MICROSERVICE_MODE'] = True

# 数据库路由配置
DATABASE_ROUTERS = ['zhi_files.database_router.ZhiFilesRouter']

# 独立数据库配置
DATABASES['files_db'] = {
    'ENGINE': 'django.db.backends.mysql',
    'NAME': 'zhi_files',
    'USER': 'your_user',
    'PASSWORD': 'your_password',
    'HOST': 'localhost',
    'PORT': '3306',
}
```

## 💻 使用示例

### Python客户端

```python
import requests

# 文件上传
def upload_file(file_path, description=""):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {'description': description}
        response = requests.post(
            'http://127.0.0.1:8000/api/enhanced-files/upload',
            files=files,
            data=data
        )
        return response.json()

# 文件下载
def download_file(file_id, save_path):
    response = requests.get(f'http://127.0.0.1:8000/api/enhanced-files/download/{file_id}')
    with open(save_path, 'wb') as f:
        f.write(response.content)
```

### JavaScript客户端

```javascript
// 文件上传
async function uploadFile(file, description = '') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('description', description);
    
    const response = await fetch('/api/enhanced-files/upload', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 获取文件列表
async function getFileList(page = 1, pageSize = 20) {
    const response = await fetch(`/api/enhanced-files/list?page=${page}&page_size=${pageSize}`);
    return await response.json();
}
```

### HTML表单

```html
<!-- 文件上传表单 -->
<form action="/api/simple/files/upload/" method="post" enctype="multipart/form-data">
    <input type="file" name="file" required>
    <input type="text" name="description" placeholder="文件描述">
    <button type="submit">上传文件</button>
</form>
```

## 📊 功能特性

### 文件管理
- ✅ 多种文件类型支持（图片、文档、视频、音频等）
- ✅ 文件大小和类型验证
- ✅ 自动文件哈希计算和去重
- ✅ 按日期组织存储结构
- ✅ 文件标签和分类管理

### 高级功能
- ✅ 文件过期管理
- ✅ 访问权限控制
- ✅ 下载统计和访问日志
- ✅ 批量文件操作
- ✅ 软删除和恢复机制

### 性能优化
- ✅ 文件缓存机制
- ✅ 分片上传支持
- ✅ 并发上传控制
- ✅ 数据库查询优化

### 监控和维护
- ✅ 详细的访问日志
- ✅ 文件使用统计
- ✅ 自动清理过期文件
- ✅ 孤儿文件检测和清理

## 🔧 开发和测试

### 运行测试

```bash
# 基础功能测试
python test_api_functionality.py

# 增强API测试
python test_enhanced_api.py

# Django单元测试
python manage.py test zhi_files --settings=application.settings.zhi_files
```

### 数据库操作

```bash
# 创建迁移文件
python manage.py makemigrations zhi_files --settings=application.settings.zhi_files

# 应用迁移
python manage.py migrate --settings=application.settings.zhi_files

# 创建超级用户
python manage.py createsuperuser --settings=application.settings.zhi_files
```

## 🚀 部署建议

### 开发环境
- 使用内置开发服务器
- SQLite数据库
- 本地文件存储

### 生产环境
- 使用 Gunicorn + Nginx
- MySQL/PostgreSQL数据库
- 云存储服务（阿里云OSS、AWS S3等）
- Redis缓存
- 定时任务清理

### 微服务部署
- 独立的数据库实例
- 容器化部署（Docker）
- 负载均衡
- 服务发现和注册

## 📝 更新日志

### v2.0.0 (2025-08-04)
- ✅ 完全重构，基于项目整体架构
- ✅ 集成CoreModel，支持完整审计功能
- ✅ 新增增强API，基于ninja-extra
- ✅ 添加文件标签和元数据支持
- ✅ 实现访问日志和统计功能
- ✅ 新增管理命令工具
- ✅ 支持微服务架构部署
- ✅ 完善的API文档和测试

### v1.0.0 (2025-07-29)
- ✅ 基础文件上传下载功能
- ✅ 简单的文件管理界面
- ✅ 基础API接口

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查依赖包
pip install -r requirements.txt

# 检查数据库连接
python manage.py check --settings=application.settings.zhi_files

# 重新生成迁移文件
python manage.py makemigrations zhi_files --settings=application.settings.zhi_files
```

#### 2. 文件上传失败
- 检查文件大小是否超过限制
- 验证文件类型是否在允许列表中
- 确认存储目录权限
- 查看服务器日志获取详细错误信息

#### 3. API认证问题
```python
# 临时禁用认证（仅开发环境）
# 在 enhanced_file_api.py 中移除 permissions=[IsAuthenticated]

# 或配置正确的认证令牌
headers = {'Authorization': 'Bearer your_token_here'}
```

#### 4. 数据库迁移问题
```bash
# 重置迁移（谨慎使用）
rm zhi_files/migrations/0*.py
python manage.py makemigrations zhi_files --settings=application.settings.zhi_files
python manage.py migrate --settings=application.settings.zhi_files
```

### 性能优化建议

#### 1. 数据库优化
```python
# 添加数据库索引
class FileStorage(CoreModel):
    class Meta:
        indexes = [
            models.Index(fields=['file_type', 'created_at']),
            models.Index(fields=['creator_id', 'is_deleted']),
            models.Index(fields=['expires_at']),
        ]
```

#### 2. 缓存配置
```python
# Redis缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

#### 3. 文件存储优化
```python
# 云存储配置示例（阿里云OSS）
DEFAULT_FILE_STORAGE = 'django_oss_storage.backends.OssMediaStorage'
OSS_ACCESS_KEY_ID = 'your_access_key'
OSS_ACCESS_KEY_SECRET = 'your_secret_key'
OSS_BUCKET_NAME = 'your_bucket_name'
OSS_ENDPOINT = 'oss-cn-hangzhou.aliyuncs.com'
```

## 🔐 安全配置

### 文件安全
```python
# 文件类型白名单
ALLOWED_FILE_TYPES = {
    'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
    'archive': ['.zip', '.rar', '.7z', '.tar', '.gz'],
}

# 危险文件类型黑名单
BLOCKED_FILE_TYPES = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
```

### 访问控制
```python
# IP白名单配置
ALLOWED_IPS = ['127.0.0.1', '***********/24']

# 文件访问权限检查
def check_file_permission(user, file_record):
    if file_record.is_public:
        return True
    if user.is_authenticated and user.id == file_record.creator_id:
        return True
    return False
```

## 📈 监控和日志

### 日志配置
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/zhi_files.log',
        },
    },
    'loggers': {
        'zhi_files': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 监控指标
- 文件上传成功率
- 平均上传时间
- 存储空间使用情况
- API响应时间
- 错误率统计

## 🧪 测试覆盖

### 单元测试
```python
# tests/test_models.py
class FileStorageTestCase(TestCase):
    def test_file_creation(self):
        file_record = FileStorage.objects.create(
            file_id='test-123',
            original_name='test.txt',
            file_size=1024,
            file_type='document'
        )
        self.assertEqual(file_record.file_id, 'test-123')
```

### API测试
```python
# tests/test_api.py
class FileAPITestCase(APITestCase):
    def test_file_upload(self):
        with open('test_file.txt', 'rb') as f:
            response = self.client.post('/api/enhanced-files/upload', {
                'file': f,
                'description': 'Test file'
            })
        self.assertEqual(response.status_code, 200)
```

## 📞 支持

如有问题或建议，请：
- 📋 提交 Issue 到项目仓库
- 📧 发送邮件至 <EMAIL>
- 📖 查看项目文档和API文档
- 💬 加入开发者交流群

---

**ZhiFiles** - 让文件管理变得简单高效！ 🚀
