# ZhiFiles API 使用示例

## 📋 API概览

ZhiFiles 提供两套API：
- **增强API**: `/api/enhanced-files/` - 功能完整，支持高级特性
- **简化API**: `/api/simple/files/` - 简单易用，快速集成

## 🔗 增强API示例

### 1. 文件上传（带高级选项）

```bash
curl -X POST "http://127.0.0.1:8001/api/enhanced-files/upload" \
  -F "file=@document.pdf" \
  -F "description=重要文档" \
  -F "tags=[\"工作\", \"重要\"]" \
  -F "is_public=false" \
  -F "expires_hours=168" \
  -F "category=document"
```

**响应示例**:
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "file_id": "abc123-def456-ghi789",
    "original_name": "document.pdf",
    "file_size": 1048576,
    "file_type": "document",
    "content_type": "application/pdf",
    "tags": ["工作", "重要"],
    "description": "重要文档",
    "expires_at": "2025-08-11T14:00:00Z",
    "created_at": "2025-08-04T14:00:00Z"
  }
}
```

### 2. 文件列表（带过滤）

```bash
curl "http://127.0.0.1:8001/api/enhanced-files/list?file_type=document&search=重要&page=1&page_size=10"
```

### 3. 文件详细信息

```bash
curl "http://127.0.0.1:8001/api/enhanced-files/info/abc123-def456-ghi789"
```

### 4. 更新文件信息

```bash
curl -X PUT "http://127.0.0.1:8001/api/enhanced-files/update/abc123-def456-ghi789" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "更新后的描述",
    "tags": ["工作", "重要", "已审核"],
    "expires_hours": 336
  }'
```

### 5. 批量操作

```bash
# 批量删除
curl -X POST "http://127.0.0.1:8001/api/enhanced-files/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "file_ids": ["id1", "id2", "id3"],
    "operation": "delete"
  }'

# 批量更新标签
curl -X POST "http://127.0.0.1:8001/api/enhanced-files/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "file_ids": ["id1", "id2"],
    "operation": "update_tags",
    "data": {"tags": ["新标签", "批量更新"]}
  }'
```

## 🔗 简化API示例

### 1. 简单文件上传

```bash
curl -X POST "http://127.0.0.1:8001/api/simple/files/upload/" \
  -F "file=@image.jpg" \
  -F "description=测试图片"
```

### 2. 获取文件列表

```bash
curl "http://127.0.0.1:8001/api/simple/files/list/?page=1&page_size=20"
```

### 3. 下载文件

```bash
curl "http://127.0.0.1:8001/api/simple/files/download/abc123-def456-ghi789/" -O
```

## 💻 编程语言示例

### Python

```python
import requests
import json

class ZhiFilesClient:
    def __init__(self, base_url="http://127.0.0.1:8001"):
        self.base_url = base_url
        self.enhanced_api = f"{base_url}/api/enhanced-files"
        self.simple_api = f"{base_url}/api/simple/files"
    
    def upload_file(self, file_path, description="", tags=None, category=None):
        """上传文件"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'description': description,
                'tags': json.dumps(tags or []),
                'category': category or ''
            }
            response = requests.post(f"{self.enhanced_api}/upload", files=files, data=data)
            return response.json()
    
    def get_file_list(self, page=1, page_size=20, file_type=None, search=None):
        """获取文件列表"""
        params = {'page': page, 'page_size': page_size}
        if file_type:
            params['file_type'] = file_type
        if search:
            params['search'] = search
        
        response = requests.get(f"{self.enhanced_api}/list", params=params)
        return response.json()
    
    def download_file(self, file_id, save_path):
        """下载文件"""
        response = requests.get(f"{self.enhanced_api}/download/{file_id}")
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        return False
    
    def get_file_info(self, file_id):
        """获取文件信息"""
        response = requests.get(f"{self.enhanced_api}/info/{file_id}")
        return response.json()

# 使用示例
client = ZhiFilesClient()

# 上传文件
result = client.upload_file(
    'document.pdf', 
    description='重要文档',
    tags=['工作', '重要'],
    category='document'
)
print(f"上传结果: {result}")

# 获取文件列表
files = client.get_file_list(file_type='document', search='重要')
print(f"文件列表: {files}")
```

### JavaScript (Node.js)

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class ZhiFilesClient {
    constructor(baseUrl = 'http://127.0.0.1:8001') {
        this.baseUrl = baseUrl;
        this.enhancedApi = `${baseUrl}/api/enhanced-files`;
        this.simpleApi = `${baseUrl}/api/simple/files`;
    }
    
    async uploadFile(filePath, options = {}) {
        const form = new FormData();
        form.append('file', fs.createReadStream(filePath));
        form.append('description', options.description || '');
        form.append('tags', JSON.stringify(options.tags || []));
        form.append('category', options.category || '');
        
        try {
            const response = await axios.post(`${this.enhancedApi}/upload`, form, {
                headers: form.getHeaders()
            });
            return response.data;
        } catch (error) {
            throw new Error(`上传失败: ${error.message}`);
        }
    }
    
    async getFileList(options = {}) {
        const params = {
            page: options.page || 1,
            page_size: options.pageSize || 20
        };
        
        if (options.fileType) params.file_type = options.fileType;
        if (options.search) params.search = options.search;
        
        try {
            const response = await axios.get(`${this.enhancedApi}/list`, { params });
            return response.data;
        } catch (error) {
            throw new Error(`获取列表失败: ${error.message}`);
        }
    }
    
    async downloadFile(fileId, savePath) {
        try {
            const response = await axios.get(`${this.enhancedApi}/download/${fileId}`, {
                responseType: 'stream'
            });
            
            const writer = fs.createWriteStream(savePath);
            response.data.pipe(writer);
            
            return new Promise((resolve, reject) => {
                writer.on('finish', resolve);
                writer.on('error', reject);
            });
        } catch (error) {
            throw new Error(`下载失败: ${error.message}`);
        }
    }
}

// 使用示例
(async () => {
    const client = new ZhiFilesClient();
    
    try {
        // 上传文件
        const uploadResult = await client.uploadFile('document.pdf', {
            description: '重要文档',
            tags: ['工作', '重要'],
            category: 'document'
        });
        console.log('上传结果:', uploadResult);
        
        // 获取文件列表
        const fileList = await client.getFileList({
            fileType: 'document',
            search: '重要'
        });
        console.log('文件列表:', fileList);
        
    } catch (error) {
        console.error('操作失败:', error.message);
    }
})();
```

### JavaScript (浏览器)

```html
<!DOCTYPE html>
<html>
<head>
    <title>ZhiFiles 文件上传</title>
</head>
<body>
    <form id="uploadForm">
        <input type="file" id="fileInput" required>
        <input type="text" id="description" placeholder="文件描述">
        <input type="text" id="tags" placeholder="标签（用逗号分隔）">
        <button type="submit">上传文件</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const description = document.getElementById('description').value;
            const tags = document.getElementById('tags').value.split(',').map(tag => tag.trim());
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('description', description);
            formData.append('tags', JSON.stringify(tags));
            
            try {
                const response = await fetch('/api/enhanced-files/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = 
                    `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    `<p style="color: red;">上传失败: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
```

## 🔐 认证示例

### 使用Bearer Token

```bash
# 获取访问令牌（示例）
TOKEN="your_access_token_here"

# 带认证的API请求
curl -X POST "http://127.0.0.1:8001/api/enhanced-files/upload" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@document.pdf" \
  -F "description=认证上传测试"
```

### Python认证示例

```python
import requests

class AuthenticatedZhiFilesClient:
    def __init__(self, base_url, access_token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {access_token}'}
    
    def upload_file(self, file_path, **kwargs):
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(
                f"{self.base_url}/api/enhanced-files/upload",
                files=files,
                data=kwargs,
                headers=self.headers
            )
            return response.json()
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "data": null
}
```

## 🔍 调试技巧

### 1. 查看详细错误信息
```bash
curl -v "http://127.0.0.1:8001/api/enhanced-files/upload" \
  -F "file=@test.txt"
```

### 2. 检查API文档
访问 http://127.0.0.1:8001/api/docs/ 查看交互式API文档

### 3. 查看服务器日志
```bash
tail -f logs/zhi_files.log
```

---

更多API详情请参考 [README.md](README.md) 或访问在线API文档。
