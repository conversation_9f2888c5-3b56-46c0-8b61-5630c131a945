#!/usr/bin/env python
"""
ZhiFiles 增强API功能测试脚本
测试完整的增强API功能，包括ninja-extra的高级特性
"""

import requests
import json
import tempfile
import os
from pathlib import Path

# API 基础URL
BASE_URL = "http://127.0.0.1:8001/api"
ENHANCED_API_URL = f"{BASE_URL}/enhanced-files"
SIMPLE_API_URL = f"{BASE_URL}/simple/files"

def create_test_file(content="这是一个测试文件\n用于测试ZhiFiles增强API\n"):
    """创建测试文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name

def test_api_docs():
    """测试API文档"""
    print("\n=== 测试API文档 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/docs/")
        if response.status_code == 200:
            print("✅ API文档访问正常")
            print(f"   文档地址: {BASE_URL}/docs/")
            return True
        else:
            print(f"❌ API文档访问失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")
    
    return False

def test_openapi_schema():
    """测试OpenAPI Schema"""
    print("\n=== 测试OpenAPI Schema ===")
    
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            schema = response.json()
            print("✅ OpenAPI Schema获取成功")
            print(f"   API标题: {schema.get('info', {}).get('title', 'N/A')}")
            print(f"   API版本: {schema.get('info', {}).get('version', 'N/A')}")
            
            # 检查路径
            paths = schema.get('paths', {})
            enhanced_paths = [path for path in paths.keys() if 'enhanced-files' in path]
            print(f"   增强API路径数量: {len(enhanced_paths)}")
            
            if enhanced_paths:
                print("   增强API路径:")
                for path in enhanced_paths[:5]:  # 显示前5个
                    print(f"     - {path}")
            
            return True
        else:
            print(f"❌ OpenAPI Schema获取失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ OpenAPI Schema获取异常: {e}")
    
    return False

def test_enhanced_file_upload():
    """测试增强文件上传"""
    print("\n=== 测试增强文件上传 ===")
    headers = {
        "Authorization": "Bearer access-8f137cefc3fc034f3f25215ac7c9b8935a45d5959bf600b468",
        "Content-Type": "application/octet-stream",
    }
    
    test_file_path = create_test_file()
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': ('enhanced_test.txt', f, 'text/plain')}
            data = {
                'description': '增强API测试文件',
                'tags': '["测试", "增强API"]',
                'is_public': 'false',
                'expires_hours': '24',
                'category': 'test'
            }
            
            response = requests.post(f"{ENHANCED_API_URL}/upload", headers=headers, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 增强文件上传成功")
                    file_data = result.get('data', {})
                    print(f"   文件ID: {file_data.get('file_id')}")
                    print(f"   文件名: {file_data.get('original_name')}")
                    print(f"   文件大小: {file_data.get('file_size')} 字节")
                    print(f"   文件类型: {file_data.get('file_type')}")
                    print(f"   标签: {file_data.get('tags')}")
                    print(f"   描述: {file_data.get('description')}")
                    return file_data.get('file_id')
                else:
                    print(f"❌ 增强上传失败: {result.get('message')}")
            else:
                print(f"❌ 增强上传失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text}")
    
    except Exception as e:
        print(f"❌ 增强上传异常: {e}")
    
    finally:
        try:
            os.unlink(test_file_path)
        except:
            pass
    
    return None

def test_enhanced_file_list():
    """测试增强文件列表"""
    print("\n=== 测试增强文件列表 ===")
    
    try:
        # 测试基础列表
        response = requests.get(f"{ENHANCED_API_URL}/list")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 增强文件列表获取成功")
                data = result.get('data', {})
                
                if isinstance(data, list):
                    files = data
                    print(f"   文件数量: {len(files)}")
                    
                    if files:
                        print("   文件列表:")
                        for i, file_info in enumerate(files[:3], 1):
                            print(f"     {i}. {file_info.get('original_name')} "
                                  f"({file_info.get('file_size')} 字节)")
                else:
                    print("   响应格式不符合预期")
                
                return True
            else:
                print(f"❌ 增强列表获取失败: {result.get('message')}")
        else:
            print(f"❌ 增强列表获取失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
    
    except Exception as e:
        print(f"❌ 增强列表获取异常: {e}")
    
    return False

def test_enhanced_file_info(file_id):
    """测试增强文件信息获取"""
    print(f"\n=== 测试增强文件信息获取 (ID: {file_id}) ===")
    
    if not file_id:
        print("❌ 没有可查询的文件ID")
        return False
    
    try:
        response = requests.get(f"{ENHANCED_API_URL}/info/{file_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 增强文件信息获取成功")
                file_data = result.get('data', {})
                print(f"   文件ID: {file_data.get('file_id')}")
                print(f"   原始名称: {file_data.get('original_name')}")
                print(f"   文件大小: {file_data.get('file_size')} 字节")
                print(f"   文件类型: {file_data.get('file_type')}")
                print(f"   内容类型: {file_data.get('content_type')}")
                print(f"   状态: {file_data.get('status')}")
                print(f"   下载次数: {file_data.get('download_count')}")
                print(f"   创建时间: {file_data.get('created_at')}")
                return True
            else:
                print(f"❌ 增强信息获取失败: {result.get('message')}")
        else:
            print(f"❌ 增强信息获取失败: HTTP {response.status_code}")
    
    except Exception as e:
        print(f"❌ 增强信息获取异常: {e}")
    
    return False

def test_enhanced_file_download(file_id):
    """测试增强文件下载"""
    print(f"\n=== 测试增强文件下载 (ID: {file_id}) ===")
    
    if not file_id:
        print("❌ 没有可下载的文件ID")
        return False
    
    try:
        response = requests.get(f"{ENHANCED_API_URL}/download/{file_id}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                result = response.json()
                print(f"❌ 增强下载失败: {result.get('message')}")
                return False
            else:
                print("✅ 增强文件下载成功")
                print(f"   内容类型: {content_type}")
                print(f"   文件大小: {len(response.content)} 字节")
                
                # 验证文件内容
                content = response.content.decode('utf-8', errors='ignore')
                if '增强API测试文件' in content or '测试文件' in content:
                    print("   ✅ 文件内容验证通过")
                else:
                    print("   ⚠️  文件内容验证失败")
                
                return True
        else:
            print(f"❌ 增强下载失败: HTTP {response.status_code}")
    
    except Exception as e:
        print(f"❌ 增强下载异常: {e}")
    
    return False

def test_simple_api_compatibility():
    """测试简化API兼容性"""
    print("\n=== 测试简化API兼容性 ===")
    
    try:
        response = requests.get(f"{SIMPLE_API_URL}/list/")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 简化API兼容性正常")
                return True
            else:
                print(f"❌ 简化API响应失败: {result.get('message')}")
        else:
            print(f"❌ 简化API访问失败: HTTP {response.status_code}")
    
    except Exception as e:
        print(f"❌ 简化API测试异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🚀 开始ZhiFiles增强API功能测试")
    print("=" * 60)
    
    tests = [
        ("API文档访问", test_api_docs),
        ("OpenAPI Schema", test_openapi_schema),
        ("简化API兼容性", test_simple_api_compatibility),
    ]
    
    # 基础测试
    passed_basic = 0
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行测试: {test_name}")
        print(f"{'='*50}")
        
        if test_func():
            passed_basic += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    # 增强API功能测试
    print(f"\n{'='*50}")
    print("执行增强API功能测试")
    print(f"{'='*50}")
    
    uploaded_file_id = test_enhanced_file_upload()
    
    if uploaded_file_id:
        test_enhanced_file_list()
        test_enhanced_file_info(uploaded_file_id)
        test_enhanced_file_download(uploaded_file_id)
        passed_enhanced = 4
    else:
        passed_enhanced = 0
    
    # 总结
    total_tests = len(tests) + 4  # 基础测试 + 4个增强API测试
    total_passed = passed_basic + passed_enhanced
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {total_passed}/{total_tests} 通过")
    print(f"{'='*60}")
    
    if total_passed >= total_tests - 1:  # 允许1个测试失败
        print("🎉 ZhiFiles增强API测试基本通过！")
        print("\n💡 可用功能:")
        print("   📖 API文档: http://127.0.0.1:8001/api/zhi_files/docs/")
        print("   🔗 增强API: http://127.0.0.1:8001/api/enhanced-files/")
        print("   🔗 简化API: http://127.0.0.1:8001/api/simple/files/")
        print("   📊 管理后台: http://127.0.0.1:8001/admin/")
        
        print("\n🛠️  管理命令:")
        print("   python manage.py file_stats --detailed")
        print("   python manage.py cleanup_files --expired --dry-run")
        
        return True
    else:
        print("⚠️  部分测试失败，请检查服务器状态")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
