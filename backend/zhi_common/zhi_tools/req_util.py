"""
优化后的Request工具类
"""
import json
import logging
from typing import Any, Dict, Union

from django.contrib.auth.models import AbstractBaseUser, AnonymousUser
from django.http import HttpRequest
# 安全导入user_agents
try:
    from user_agents import parse
except ImportError:
    # 如果user_agents不可用，创建一个简单的替代函数
    def parse(user_agent_string):
        class MockUserAgent:
            def __init__(self, ua_string):
                self.ua_string = ua_string or ""

            @property
            def browser(self):
                class MockBrowser:
                    family = "Unknown"
                    version_string = "Unknown"
                return MockBrowser()

            @property
            def os(self):
                class MockOS:
                    family = "Unknown"
                    version_string = "Unknown"
                return MockOS()

            @property
            def device(self):
                class MockDevice:
                    family = "Unknown"
                return MockDevice()

        return MockUserAgent(user_agent_string)

from zhi_logger.models import LoginLog
from .g_local_thread_user_info import get_request_user_info


logger = logging.getLogger(__name__)
from zhi_common.zhi_tools.g_local_thread_user_info import g_thread_locals


def get_request_trace_id(request: HttpRequest = None) -> Union[None,  str]:
    """
    获取请求用户（优化版）
    1. 优先从request.user 获取已认证用户
    2. 未认证时尝试从token解析用户
    3. 添加类型注解和完整错误处理
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)
    trace_id = getattr(request, 'trace_id', None)

    return trace_id


def get_request_ip(request: HttpRequest = None) -> str:
    """
    获取客户端IP（优化版）
    1. 支持X-Forwarded-For链式IP
    2. 添加类型注解和默认值处理
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)
    ip = None
    try:
        # 处理代理服务器场景
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR', '')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[-1].strip()

        # 直接连接IP
        if not ip:
            ip = request.META.get('REMOTE_ADDR', '')

        # 从请求属性获取
        if not ip:
            ip = getattr(request, 'request_ip', None)

    except Exception as e:
        logger.debug(f" 获取IP失败: {str(e)}", exc_info=True)

    return ip or 'unknown'


def get_request_data(request: HttpRequest = None) -> Dict[str, Any]:
    """
    获取请求参数（优化版）
    1. 合并GET/POST参数
    2. 支持JSON body解析
    3. 添加类型注解和错误处理
    4. 修复文件上传时的RawPostDataException问题
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)

    if not request:
        return {}

    try:
        # 优先从请求属性获取
        request_data = getattr(request, 'request_data', None)
        if request_data and isinstance(request_data, dict):
            return request_data

        # 合并GET/POST参数
        data = {**request.GET.dict(), **request.POST.dict()}

        # 处理文件上传请求
        if hasattr(request, 'FILES') and request.FILES:
            # 文件上传请求，记录文件信息而不是内容
            files_info = {}
            for field_name, uploaded_file in request.FILES.items():
                if hasattr(uploaded_file, 'multiple') and uploaded_file.multiple:
                    # 多文件上传
                    files_info[field_name] = [
                        {
                            'name': f.name,
                            'size': f.size,
                            'content_type': getattr(f, 'content_type', 'unknown')
                        } for f in uploaded_file
                    ]
                else:
                    # 单文件上传
                    files_info[field_name] = {
                        'name': uploaded_file.name,
                        'size': uploaded_file.size,
                        'content_type': getattr(uploaded_file, 'content_type', 'unknown')
                    }
            data['_files'] = files_info
            return data

        # 处理JSON body（仅在非文件上传且没有POST数据时）
        if not data:
            try:
                # 检查是否可以安全访问body
                if hasattr(request, '_body'):
                    # body已经被读取过，使用缓存的内容
                    body_content = request._body
                elif hasattr(request, 'body'):
                    # 尝试读取body，但要处理可能的异常
                    try:
                        body_content = request.body
                    except Exception:
                        # 如果无法读取body（如已被消费），返回空数据
                        return data
                else:
                    return data

                if body_content:
                    try:
                        body_data = json.loads(body_content)
                        if isinstance(body_data, dict):
                            data = body_data
                        else:
                            data = {'data': body_data}
                    except json.JSONDecodeError:
                        # 非JSON内容，记录为原始数据（截断长内容）
                        body_str = str(body_content)
                        if len(body_str) > 500:
                            body_str = body_str[:500] + '...[truncated]'
                        data = {'raw_body': body_str}

            except Exception as e:
                logger.debug(f"处理请求体失败: {str(e)}")
                # 继续使用已有的GET/POST数据

    except Exception as e:
        logger.debug(f"获取请求参数失败: {str(e)}", exc_info=True)
        data = {}

    return data


def get_request_path(request: HttpRequest = None, *args, **kwargs) -> str:
    """
    获取请求路径（优化版）
    1. 支持路径参数替换
    2. 添加类型注解和默认处理
    """
    if not  request:
        request = getattr(g_thread_locals, 'request', None)
    try:
        # 优先从请求属性获取
        request_path = getattr(request, 'request_path', None)
        if request_path:
            return request_path

        # 处理路径参数替换
        path = request.path
        if args:
            values = []
            for arg in args:
                if not arg:
                    continue
                if isinstance(arg, str):
                    values.append(arg)
                elif isinstance(arg, (tuple, list, set)):
                    values.extend(arg)
                elif isinstance(arg, dict):
                    values.extend(arg.values())

            for value in values:
                path = path.replace(f'/{value}', '/{id}')

        return path

    except Exception as e:
        logger.debug(f" 获取请求路径失败: {str(e)}", exc_info=True)
        return request.path


def get_request_canonical_path(request: HttpRequest = None) -> str:
    """
    获取规范化请求路径（优化版）
    1. 使用resolver_match处理动态路径
    2. 添加类型注解和错误处理
    """
    if not request:
        request = request = getattr(g_thread_locals, 'request', None)
    try:
        # 优先从请求属性获取
        request_path = getattr(request, 'request_canonical_path', None)
        if request_path:
            return request_path

        path = request.path
        resolver_match = getattr(request, 'resolver_match', None)
        if resolver_match:
            # 处理位置参数
            for value in resolver_match.args:
                path = path.replace(f'/{value}', '/{id}')

            # 处理关键字参数
            for key, value in resolver_match.kwargs.items():
                placeholder = f'{{{key}}}' if key != 'pk' else '{id}'
                path = path.replace(f'/{value}', f'/{placeholder}')

        return path

    except Exception as e:
        logger.debug(f" 获取规范化路径失败: {str(e)}", exc_info=True)
        return request.path


def get_browser(request: HttpRequest = None) -> str:
    """
    获取浏览器信息（优化版）
    1. 添加类型注解和错误处理
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)
    try:
        ua_string = request.META.get('HTTP_USER_AGENT', '')
        if ua_string:
            return parse(ua_string).browser.family
    except Exception as e:
        logger.debug(f" 获取浏览器信息失败: {str(e)}", exc_info=True)
    return 'Unknown'


def get_os(request: HttpRequest = None) -> str:
    """
    获取操作系统信息（优化版）
    1. 添加类型注解和错误处理
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)
    try:
        ua_string = request.META.get('HTTP_USER_AGENT', '')
        if ua_string:
            return parse(ua_string).os.family
    except Exception as e:
        logger.debug(f" 获取操作系统信息失败: {str(e)}", exc_info=True)
    return 'Unknown'


def get_verbose_name(queryset=None, view=None, model=None) -> str:
    """
    获取模型verbose_name（优化版）
    1. 支持多种方式获取模型
    2. 添加类型注解和错误处理
    """
    try:
        if model and hasattr(model, '_meta'):
            return model._meta.verbose_name

        if queryset and hasattr(queryset, 'model'):
            return queryset.model._meta.verbose_name

        if view:
            # 从视图的queryset获取
            if hasattr(view, 'get_queryset'):
                queryset = view.get_queryset()
                if hasattr(queryset, 'model'):
                    return queryset.model._meta.verbose_name

            # 从序列化器获取
            if hasattr(view, 'get_serializer'):
                serializer = view.get_serializer()
                if hasattr(serializer, 'Meta') and hasattr(serializer.Meta, 'model'):
                    return serializer.Meta.model._meta.verbose_name

    except Exception as e:
        logger.debug(f" 获取verbose_name失败: {str(e)}", exc_info=True)

    return ""


def save_login_log(request: HttpRequest = None, user_info: dict = None) -> None:
    """
    保存登录日志（优化版）
    1. 添加类型注解和完整错误处理
    """
    try:
        if not request:
            request = getattr(g_thread_locals, 'request', None)
        ip = get_request_ip(request)
        user_agent = parse(request.META.get('HTTP_USER_AGENT', ''))
        print('In save_login_log----->')
        print(user_info)

        log_data = {
            'username': user_info.get('username'),
            'ip': ip,
            'agent': str(user_agent),
            'browser': user_agent.browser.family,
            'os': user_agent.os.family,
            'creator_id': user_info.get('id'),
            'org_id': user_info.get('dept')
            }

        LoginLog.objects.create(**log_data)

    except Exception as e:
        logger.error(f" 保存登录日志失败: {str(e)}", exc_info=True)


def get_client_ip(request: HttpRequest = None) -> str:
    """
    获取客户端IP地址（别名函数）
    为了兼容性，提供get_client_ip别名
    """
    return get_request_ip(request)


def get_user_agent(request: HttpRequest = None) -> str:
    """
    获取用户代理字符串
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)

    try:
        return request.META.get('HTTP_USER_AGENT', '') if request else ''
    except Exception as e:
        logger.debug(f"获取用户代理失败: {str(e)}", exc_info=True)
        return ''
