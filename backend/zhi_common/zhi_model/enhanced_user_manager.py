"""
增强的用户信息管理器 - 支持批量操作时自动填充用户信息

包括 bulk_create、create、update 等操作的自动用户信息填充
"""

from typing import List, Dict, Any, Optional, Union
from django.db import models, transaction
from django.contrib.auth import get_user_model
from django.core.cache import cache
from zhi_common.zhi_model.user_info_manager import user_info_manager
from zhi_common.zhi_tools.g_local_thread_user_info import get_request_user_info
from zhi_common.zhi_logger import get_logger

logger = get_logger(module_name="enhanced_user_manager")


class EnhancedUserManager:
    """
    增强的用户信息管理器
    
    支持在各种数据库操作中自动填充用户信息：
    - bulk_create: 批量创建时自动填充创建人信息
    - create: 单个创建时自动填充创建人信息
    - update: 更新时自动填充修改人信息
    - bulk_update: 批量更新时自动填充修改人信息
    """
    
    def __init__(self):
        self.user_info_manager = user_info_manager
    
    @staticmethod
    def get_current_user_info() -> Dict[str, Any]:
        """获取当前请求的用户信息"""
        try:
            user_info = get_request_user_info()
            return {
                'user_id': user_info.get('id'),
                'username': user_info.get('username'),
                'org_id': user_info.get('org_id'),
            }
        except Exception as e:
            logger.warning(f"获取当前用户信息失败: {e}")
            return {
                'user_id': None,
                'username': None,
                'org_id': None,
            }
    
    def prepare_create_data(self, data: Dict[str, Any], user_id: str = None, model_class=None) -> Dict[str, Any]:
        """
        为创建操作准备数据，自动填充用户信息

        Args:
            data: 原始数据
            user_id: 指定的用户ID，如果不提供则使用当前用户
            model_class: 模型类，用于检查字段是否存在

        Returns:
            dict: 填充了用户信息的数据
        """
        current_user = self.get_current_user_info()
        target_user_id = user_id or current_user['user_id']

        if not target_user_id:
            return data

        # 获取用户详细信息
        user_info = self.user_info_manager.get_user_info(target_user_id)
        if not user_info:
            return data

        # 填充创建人信息（如果字段不存在则不填充）
        enhanced_data = data.copy()

        # 检查模型是否有相应字段的辅助函数
        def has_field(field_name):
            if model_class:
                return hasattr(model_class, field_name)
            return True  # 如果没有提供模型类，默认认为字段存在

        if has_field('creator_id') and ('creator_id' not in enhanced_data or not enhanced_data['creator_id']):
            enhanced_data['creator_id'] = user_info['id']

        if has_field('creator_name') and ('creator_name' not in enhanced_data or not enhanced_data['creator_name']):
            enhanced_data['creator_name'] = user_info['name']

        # 填充组织信息（只有模型有org_id字段时才填充）
        if has_field('org_id') and ('org_id' not in enhanced_data or not enhanced_data['org_id']):
            enhanced_data['org_id'] = current_user['org_id']

        # 同时设置修改人信息（创建时创建人和修改人相同）
        if has_field('modifier_id') and ('modifier_id' not in enhanced_data or not enhanced_data['modifier_id']):
            enhanced_data['modifier_id'] = user_info['id']

        if has_field('modifier_name') and ('modifier_name' not in enhanced_data or not enhanced_data['modifier_name']):
            enhanced_data['modifier_name'] = user_info['name']

        return enhanced_data
    
    def prepare_update_data(self, data: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """
        为更新操作准备数据，自动填充修改人信息
        
        Args:
            data: 原始数据
            user_id: 指定的用户ID，如果不提供则使用当前用户
            
        Returns:
            dict: 填充了修改人信息的数据
        """
        current_user = self.get_current_user_info()
        target_user_id = user_id or current_user['user_id']
        
        if not target_user_id:
            return data
        
        # 获取用户详细信息
        user_info = self.user_info_manager.get_user_info(target_user_id)
        if not user_info:
            return data
        
        # 填充修改人信息
        enhanced_data = data.copy()
        
        if 'modifier_id' not in enhanced_data or not enhanced_data['modifier_id']:
            enhanced_data['modifier_id'] = user_info['id']
        
        if 'modifier_name' not in enhanced_data or not enhanced_data['modifier_name']:
            enhanced_data['modifier_name'] = user_info['name']
        
        return enhanced_data
    
    def enhanced_create(self, model_class: models.Model, data: Dict[str, Any], user_id: str = None) -> models.Model:
        """
        增强的创建方法，自动填充用户信息
        
        Args:
            model_class: 模型类
            data: 创建数据
            user_id: 指定的用户ID
            
        Returns:
            创建的模型实例
        """
        enhanced_data = self.prepare_create_data(data, user_id)
        
        try:
            instance = model_class.objects.create(**enhanced_data)
            logger.info(f"增强创建成功: {model_class.__name__} - {instance.pk}")
            return instance
        except Exception as e:
            logger.error(f"增强创建失败: {model_class.__name__} - {e}")
            raise
    
    def enhanced_bulk_create(
        self, 
        model_class: models.Model, 
        data_list: List[Dict[str, Any]], 
        user_id: str = None,
        batch_size: int = 1000
    ) -> List[models.Model]:
        """
        增强的批量创建方法，自动填充用户信息
        
        Args:
            model_class: 模型类
            data_list: 创建数据列表
            user_id: 指定的用户ID
            batch_size: 批量大小
            
        Returns:
            创建的模型实例列表
        """
        if not data_list:
            return []
        
        # 预处理所有数据，填充用户信息
        enhanced_data_list = []
        for data in data_list:
            enhanced_data = self.prepare_create_data(data, user_id)
            enhanced_data_list.append(enhanced_data)
        
        try:
            # 创建模型实例对象
            instances = []
            for data in enhanced_data_list:
                instance = model_class(**data)
                instances.append(instance)
            
            # 批量创建
            created_instances = model_class.objects.bulk_create(
                instances, 
                batch_size=batch_size,
                ignore_conflicts=False
            )
            
            logger.info(f"增强批量创建成功: {model_class.__name__} - {len(created_instances)} 条记录")
            return created_instances
            
        except Exception as e:
            logger.error(f"增强批量创建失败: {model_class.__name__} - {e}")
            raise
    
    def enhanced_update(
        self, 
        queryset: models.QuerySet, 
        data: Dict[str, Any], 
        user_id: str = None
    ) -> int:
        """
        增强的更新方法，自动填充修改人信息
        
        Args:
            queryset: 查询集
            data: 更新数据
            user_id: 指定的用户ID
            
        Returns:
            更新的记录数
        """
        enhanced_data = self.prepare_update_data(data, user_id)
        
        try:
            updated_count = queryset.update(**enhanced_data)
            logger.info(f"增强更新成功: {queryset.model.__name__} - {updated_count} 条记录")
            return updated_count
        except Exception as e:
            logger.error(f"增强更新失败: {queryset.model.__name__} - {e}")
            raise
    
    def enhanced_bulk_update(
        self, 
        instances: List[models.Model], 
        fields: List[str], 
        user_id: str = None,
        batch_size: int = 1000
    ) -> List[models.Model]:
        """
        增强的批量更新方法，自动填充修改人信息
        
        Args:
            instances: 模型实例列表
            fields: 要更新的字段列表
            user_id: 指定的用户ID
            batch_size: 批量大小
            
        Returns:
            更新后的模型实例列表
        """
        if not instances:
            return []
        
        current_user = self.get_current_user_info()
        target_user_id = user_id or current_user['user_id']
        
        if target_user_id:
            # 获取用户详细信息
            user_info = self.user_info_manager.get_user_info(target_user_id)
            if user_info:
                # 为所有实例设置修改人信息
                for instance in instances:
                    if hasattr(instance, 'modifier_id'):
                        instance.modifier_id = user_info['id']
                        if 'modifier_id' not in fields:
                            fields.append('modifier_id')
                    
                    if hasattr(instance, 'modifier_name'):
                        instance.modifier_name = user_info['name']
                        if 'modifier_name' not in fields:
                            fields.append('modifier_name')
        
        try:
            # 批量更新
            model_class = instances[0].__class__
            updated_instances = model_class.objects.bulk_update(
                instances, 
                fields, 
                batch_size=batch_size
            )
            
            logger.info(f"增强批量更新成功: {model_class.__name__} - {len(instances)} 条记录")
            return instances
            
        except Exception as e:
            logger.error(f"增强批量更新失败: {e}")
            raise
    
    def get_model_user_fields(self, model_class: models.Model) -> Dict[str, bool]:
        """
        获取模型的用户相关字段信息
        
        Args:
            model_class: 模型类
            
        Returns:
            dict: 字段存在情况
        """
        fields = {}
        field_names = [f.name for f in model_class._meta.fields]
        
        fields['has_creator_id'] = 'creator_id' in field_names
        fields['has_creator_name'] = 'creator_name' in field_names
        fields['has_modifier_id'] = 'modifier_id' in field_names
        fields['has_modifier_name'] = 'modifier_name' in field_names
        fields['has_org_id'] = 'org_id' in field_names
        
        return fields


# 全局实例
enhanced_user_manager = EnhancedUserManager()


# 便捷函数
def enhanced_create(model_class: models.Model, data: Dict[str, Any], user_id: str = None) -> models.Model:
    """便捷函数：增强的创建"""
    return enhanced_user_manager.enhanced_create(model_class, data, user_id)


def enhanced_bulk_create(
    model_class: models.Model, 
    data_list: List[Dict[str, Any]], 
    user_id: str = None,
    batch_size: int = 1000
) -> List[models.Model]:
    """便捷函数：增强的批量创建"""
    return enhanced_user_manager.enhanced_bulk_create(model_class, data_list, user_id, batch_size)


def enhanced_update(
    queryset: models.QuerySet, 
    data: Dict[str, Any], 
    user_id: str = None
) -> int:
    """便捷函数：增强的更新"""
    return enhanced_user_manager.enhanced_update(queryset, data, user_id)


def enhanced_bulk_update(
    instances: List[models.Model],
    fields: List[str],
    user_id: str = None,
    batch_size: int = 1000
) -> List[models.Model]:
    """便捷函数：增强的批量更新"""
    return enhanced_user_manager.enhanced_bulk_update(instances, fields, user_id, batch_size)


class EnhancedQuerySet(models.QuerySet):
    """
    增强的查询集，自动处理用户信息填充
    """

    def create(self, **kwargs):
        """重写create方法，自动填充用户信息"""
        enhanced_data = enhanced_user_manager.prepare_create_data(kwargs, model_class=self.model)
        return super().create(**enhanced_data)

    def bulk_create(self, objs, batch_size=None, ignore_conflicts=False, update_conflicts=False, update_fields=None, unique_fields=None):
        """重写bulk_create方法，自动填充用户信息"""
        if not objs:
            return []

        # 为每个对象填充用户信息
        current_user = enhanced_user_manager.get_current_user_info()
        user_id = current_user['user_id']

        if user_id:
            user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
            if user_info:
                for obj in objs:
                    # 填充创建人信息
                    if hasattr(obj, 'creator_id') and not getattr(obj, 'creator_id', None):
                        obj.creator_id = user_info['id']
                    if hasattr(obj, 'creator_name') and not getattr(obj, 'creator_name', None):
                        obj.creator_name = user_info['name']

                    # 填充修改人信息
                    if hasattr(obj, 'modifier_id') and not getattr(obj, 'modifier_id', None):
                        obj.modifier_id = user_info['id']
                    if hasattr(obj, 'modifier_name') and not getattr(obj, 'modifier_name', None):
                        obj.modifier_name = user_info['name']

                    # 填充组织信息
                    if hasattr(obj, 'org_id') and not getattr(obj, 'org_id', None):
                        obj.org_id = current_user['org_id']

        return super().bulk_create(objs, batch_size, ignore_conflicts, update_conflicts, update_fields, unique_fields)

    def update(self, **kwargs):
        """重写update方法，自动填充修改人信息"""
        enhanced_data = enhanced_user_manager.prepare_update_data(kwargs)
        return super().update(**enhanced_data)

    def bulk_update(self, objs, fields, batch_size=None):
        """重写bulk_update方法，自动填充修改人信息"""
        if not objs:
            return

        current_user = enhanced_user_manager.get_current_user_info()
        user_id = current_user['user_id']

        if user_id:
            user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
            if user_info:
                # 为所有对象设置修改人信息
                for obj in objs:
                    if hasattr(obj, 'modifier_id'):
                        obj.modifier_id = user_info['id']
                        if 'modifier_id' not in fields:
                            fields = list(fields) + ['modifier_id']

                    if hasattr(obj, 'modifier_name'):
                        obj.modifier_name = user_info['name']
                        if 'modifier_name' not in fields:
                            fields = list(fields) + ['modifier_name']

        return super().bulk_update(objs, fields, batch_size)


class EnhancedManager(models.Manager):
    """
    增强的模型管理器，使用增强的查询集
    """

    def get_queryset(self):
        return EnhancedQuerySet(self.model, using=self._db)
