#!/usr/bin/env python3
"""
检查数据库表结构的脚本
"""
import os
import django
from django.conf import settings

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

from django.db import connection
from zhi_files.models import FileStorage

def check_table_structure():
    """检查FileStorage表的结构"""
    
    print("🔍 检查FileStorage表结构...")
    
    try:
        # 获取表名
        table_name = FileStorage._meta.db_table
        print(f"📋 表名: {table_name}")
        
        # 检查表是否存在
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print("✅ 表存在")
                
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                print("\n📊 表结构:")
                print("-" * 80)
                print(f"{'字段名':<20} {'类型':<20} {'是否为空':<10} {'键':<10} {'默认值':<15} {'额外'}")
                print("-" * 80)
                
                for column in columns:
                    field, type_, null, key, default, extra = column
                    print(f"{field:<20} {type_:<20} {null:<10} {key:<10} {str(default):<15} {extra}")
                
                # 检查seq字段是否存在
                seq_exists = any(col[0] == 'seq' for col in columns)
                if seq_exists:
                    print("\n✅ seq字段存在")
                else:
                    print("\n❌ seq字段不存在")
                    
            else:
                print("❌ 表不存在")
                
        # 尝试查询一条记录
        print("\n🔍 尝试查询记录...")
        try:
            count = FileStorage.objects.count()
            print(f"✅ 查询成功，共有 {count} 条记录")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_table_structure()
