"""
ZhiCelery 异步任务管理子项目 URL 配置

提供Celery任务管理、监控和调度的Web界面
"""

from application.conf_urls.base import *
from django.urls import path, include
from zhi_celery import views

# ZhiCelery 特定的URL配置
celery_urls = [
    # Celery任务管理API
    path('api/celery/', include([
        path('tasks/', views.TaskListView.as_view(), name='celery-task-list'),
        path('tasks/<str:task_id>/', views.TaskDetailView.as_view(), name='celery-task-detail'),
        path('tasks/<str:task_id>/retry/', views.TaskRetryView.as_view(), name='celery-task-retry'),
        path('tasks/<str:task_id>/revoke/', views.TaskRevokeView.as_view(), name='celery-task-revoke'),

        # Worker管理
        path('workers/', views.WorkerListView.as_view(), name='celery-worker-list'),
        path('workers/<str:worker_name>/stats/', views.WorkerStatsView.as_view(), name='celery-worker-stats'),

        # 队列管理
        path('queues/', views.QueueListView.as_view(), name='celery-queue-list'),
        path('queues/<str:queue_name>/stats/', views.QueueStatsView.as_view(), name='celery-queue-stats'),

        # Beat调度管理
        path('schedules/', views.ScheduleListView.as_view(), name='celery-schedule-list'),
        path('schedules/<int:schedule_id>/', views.ScheduleDetailView.as_view(), name='celery-schedule-detail'),

        # 监控和统计
        path('stats/', views.CeleryStatsView.as_view(), name='celery-stats'),
        path('health/', views.CeleryHealthView.as_view(), name='celery-health'),
    ])),

    # Celery管理界面
    path('celery/', include([
        path('', views.CeleryDashboardView.as_view(), name='celery-dashboard'),
        path('tasks/', views.CeleryTasksView.as_view(), name='celery-tasks'),
        path('workers/', views.CeleryWorkersView.as_view(), name='celery-workers'),
        path('schedules/', views.CelerySchedulesView.as_view(), name='celery-schedules'),
        path('monitoring/', views.CeleryMonitoringView.as_view(), name='celery-monitoring'),
    ])),

    # API文档
    path('celery/docs/', views.CeleryDocsView.as_view(), name='celery-docs'),
]

# 添加到主URL配置
urlpatterns += celery_urls
