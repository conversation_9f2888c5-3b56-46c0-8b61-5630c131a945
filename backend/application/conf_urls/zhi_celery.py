"""
ZhiCelery 异步任务管理子项目 URL 配置

前后端分离架构，API通过 ninja-extra 提供
"""

from application.conf_urls.base import *
from django.urls import path, include
from zhi_celery import views
from zhi_celery.api import api

# ZhiCelery 特定的URL配置
celery_urls = [
    # API接口 (通过 ninja-extra 提供)
    path('api/celery/', api.urls),

    # Web管理界面 (简化的模板视图)
    path('celery/', include([
        path('', views.CeleryDashboardView.as_view(), name='celery-dashboard'),
        path('tasks/', views.CeleryTasksView.as_view(), name='celery-tasks'),
        path('workers/', views.CeleryWorkersView.as_view(), name='celery-workers'),
        path('schedules/', views.CelerySchedulesView.as_view(), name='celery-schedules'),
        path('monitoring/', views.CeleryMonitoringView.as_view(), name='celery-monitoring'),
        path('docs/', views.CeleryDocsView.as_view(), name='celery-docs'),
    ])),
]

# 添加到主URL配置
urlpatterns += celery_urls
