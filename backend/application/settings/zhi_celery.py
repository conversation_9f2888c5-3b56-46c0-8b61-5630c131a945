"""
ZhiCelery 异步任务管理子项目配置

<AUTHOR>
@Date    ：2024/10/29
"""

from application.settings.base import *
from loguru import logger


# APP 的 URL 配置
ROOT_URLCONF = 'application.conf_urls.zhi_celery'
LOGIN_URL = '/admin/login/'

# ZhiCelery 子项目特定应用（避免重复添加已存在的应用）
CELERY_APPS = [
    'django_celery_beat',  # Celery Beat调度器
    'django_celery_results',  # Celery结果存储
]

# 合并应用列表（只添加不存在的应用）
for app in CELERY_APPS:
    if app not in INSTALLED_APPS:
        INSTALLED_APPS.append(app)

# ZhiCelery 特定配置
CELERY_SETTINGS = {
    'TASK_MONITORING_ENABLED': True,
    'TASK_RESULT_RETENTION_DAYS': 7,
    'BEAT_SCHEDULER_ENABLED': True,
    'WORKER_HEALTH_CHECK_INTERVAL': 300,  # 5分钟
    'QUEUE_MONITORING_ENABLED': True,
    'TASK_RETRY_MAX_ATTEMPTS': 3,
    'TASK_TIMEOUT': 3600,  # 1小时
}

# Celery 扩展配置（覆盖base.py中的配置）
CELERY_WORKER_CONCURRENCY = 4  # 增加并发数
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True

# 启用所有队列
CELERY_TASK_ROUTES.update({
    'zhi_celery.tasks.system_tasks.*': {'queue': 'system'},
    'zhi_celery.tasks.monitoring_tasks.*': {'queue': 'system'},
})

# 中间件配置（Celery监控相关）
MIDDLEWARE += [
    'zhi_common.zhi_logger.middleware.CeleryTaskMiddleware',
]

logger.debug(f"ZhiCelery 子项目配置加载完成，应用列表: {INSTALLED_APPS}")
logger.info(f"Celery配置: {CELERY_SETTINGS}")
