#!/usr/bin/env python3
"""
ZhiCelery 异步任务管理服务启动脚本
参考其他子项目的启动脚本设计
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
backend_path = project_root / 'backend'
sys.path.insert(0, str(backend_path))

def start_celery_service(port=8004, debug=False, settings_module='application.settings.zhi_celery'):
    """启动ZhiCelery服务"""
    
    print("=" * 60)
    print("🚀 启动 ZhiCelery 异步任务管理服务")
    print("=" * 60)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', settings_module)
    os.environ.setdefault('PYTHONPATH', str(backend_path))
    
    # 检查依赖
    print("📋 检查系统依赖...")
    check_dependencies()
    
    # 检查数据库连接
    print("🔍 检查数据库连接...")
    check_database()
    
    # 检查Redis连接
    print("🔍 检查Redis连接...")
    check_redis()
    
    # 执行数据库迁移
    print("📊 执行数据库迁移...")
    run_migrations(settings_module)
    
    # 启动Django服务
    print(f"🌐 启动ZhiCelery Web服务 (端口: {port})...")
    start_django_server(port, debug, settings_module)


def check_dependencies():
    """检查系统依赖"""
    required_packages = [
        'django',
        'celery',
        'redis',
        'django-celery-beat',
        'django-celery-results',
        'ninja-extra',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        sys.exit(1)


def check_database():
    """检查数据库连接"""
    try:
        os.chdir(backend_path)
        result = subprocess.run([
            sys.executable, 'manage.py', 'check', '--database', 'default'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("  ✅ 数据库连接正常")
        else:
            print(f"  ❌ 数据库连接失败: {result.stderr}")
            sys.exit(1)
            
    except subprocess.TimeoutExpired:
        print("  ⚠️  数据库检查超时")
    except Exception as e:
        print(f"  ❌ 数据库检查异常: {e}")
        sys.exit(1)


def check_redis():
    """检查Redis连接"""
    try:
        import redis
        from django.conf import settings
        
        # 导入Django设置
        import django
        django.setup()
        
        # 检查Redis连接
        redis_client = redis.Redis.from_url(settings.REDIS_URL)
        redis_client.ping()
        print("  ✅ Redis连接正常")
        
    except Exception as e:
        print(f"  ❌ Redis连接失败: {e}")
        print("  💡 请确保Redis服务正在运行")
        sys.exit(1)


def run_migrations(settings_module):
    """执行数据库迁移"""
    try:
        os.chdir(backend_path)
        
        # 执行迁移
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate',
            '--settings', settings_module
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("  ✅ 数据库迁移完成")
        else:
            print(f"  ⚠️  数据库迁移警告: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("  ⚠️  数据库迁移超时")
    except Exception as e:
        print(f"  ❌ 数据库迁移异常: {e}")


def start_django_server(port, debug, settings_module):
    """启动Django开发服务器"""
    try:
        os.chdir(backend_path)
        
        cmd = [
            sys.executable, 'manage.py', 'runserver',
            f'0.0.0.0:{port}',
            '--settings', settings_module
        ]
        
        if not debug:
            cmd.append('--noreload')
        
        print(f"📋 启动命令: {' '.join(cmd)}")
        print(f"🌐 服务地址: http://localhost:{port}")
        print(f"📚 API文档: http://localhost:{port}/celery/docs")
        print(f"🎛️  管理界面: http://localhost:{port}/celery/")
        print("\n按 Ctrl+C 停止服务\n")
        
        # 启动服务
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='ZhiCelery 异步任务管理服务启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_celery_service.py                    # 默认启动
  python start_celery_service.py --port 8005        # 指定端口
  python start_celery_service.py --debug            # 调试模式
  python start_celery_service.py --settings application.settings.production  # 指定配置
        """
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=8004,
        help='服务端口 (默认: 8004)'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--settings',
        default='application.settings.zhi_celery',
        help='Django设置模块 (默认: application.settings.zhi_celery)'
    )
    
    args = parser.parse_args()
    
    # 启动服务
    start_celery_service(
        port=args.port,
        debug=args.debug,
        settings_module=args.settings
    )


if __name__ == '__main__':
    main()
