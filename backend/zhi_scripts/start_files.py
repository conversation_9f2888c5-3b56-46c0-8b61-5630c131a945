#!/usr/bin/env python
"""
ZhiFiles 文件管理服务启动脚本
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_files')

def main():
    """主函数"""
    try:
        # 初始化Django
        django.setup()

        # 导入Django管理命令
        from django.core.management import execute_from_command_line

        print("🚀 启动 ZhiFiles 文件管理服务...")
        print("📁 项目路径:", project_root)
        print("⚙️  配置文件: application.settings.zhi_files")
        print("🌐 API文档: http://127.0.0.1:8001/api/zhi_files/docs/")
        print("📊 管理后台: http://127.0.0.1:8001/admin/")
        print("-" * 50)

        # 检查数据库迁移
        print("🔍 检查数据库迁移...")
        try:
            from django.core.management import call_command
            # 先创建迁移文件
            call_command('makemigrations', 'zhi_files', verbosity=0)
            # 再执行迁移
            call_command('migrate', verbosity=0)
            print("✅ 数据库迁移完成")
        except Exception as e:
            print(f"⚠️  数据库迁移警告: {e}")

        # 创建必要的目录
        print("📂 创建存储目录...")
        try:
            from django.conf import settings
            media_root = Path(settings.MEDIA_ROOT)
            storage_dirs = [
                'zhi_files_uploads',
                'temp',
                'avatars',
                'documents',
                'images',
                'archives'
            ]

            for dir_name in storage_dirs:
                dir_path = media_root / dir_name
                dir_path.mkdir(parents=True, exist_ok=True)

            print("✅ 存储目录创建完成")
        except Exception as e:
            print(f"⚠️  存储目录创建警告: {e}")

        # 启动开发服务器
        print("🎯 启动开发服务器...")
        execute_from_command_line([
            'manage.py',
            'runserver',
            '0.0.0.0:8001'
        ])

    except ImportError as exc:
        raise ImportError(
            "无法导入Django。请确保已安装Django并且"
            "DJANGO_SETTINGS_MODULE环境变量已正确设置。"
        ) from exc
    except KeyboardInterrupt:
        print("\n👋 ZhiFiles 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
