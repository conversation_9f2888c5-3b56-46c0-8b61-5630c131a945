"""
CeleryTaskLog API
Celery任务日志管理接口
"""

from typing import List, Dict
from ninja import ModelSchema
from ninja_extra import http_get, http_post
from django.http import HttpRequest
from celery.result import AsyncResult

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_logger import get_logger

from ..models import CeleryTaskLog
from ..celery_app import app as celery_app

logger = get_logger(__name__)


class CeleryTaskLogSchemaIn(ModelSchema):
    """任务日志输入Schema"""
    class Meta:
        model = CeleryTaskLog
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryTaskLogSchemaOut(ModelSchema):
    """任务日志输出Schema"""
    class Meta:
        model = CeleryTaskLog
        fields = '__all__'


@auto_crud_api(
    CeleryTaskLog,
    prefix="celery_task_logs",
    tags=["Celery任务日志"],
    schema_in=CeleryTaskLogSchemaIn,
    schema_out=CeleryTaskLogSchemaOut,
    exclude=[]
)
class CeleryTaskLogControllerAPI(BaseModelService):
    """Celery任务日志管理 - 自动CRUD API"""
    model = CeleryTaskLog
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/active_tasks", response={200: BaseResponseSchema[List[Dict]]})
    def get_active_tasks(self, request: HttpRequest):
        """获取当前活跃任务"""
        try:
            inspect = celery_app.control.inspect()
            active_tasks = inspect.active() or {}
            
            tasks = []
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['id'],
                        'name': task['name'],
                        'args': task['args'],
                        'kwargs': task['kwargs'],
                        'worker': worker,
                        'time_start': task.get('time_start'),
                    })
            
            return ZhiResponse(
                data=tasks,
                message=f"获取活跃任务成功，共{len(tasks)}个任务"
            )
            
        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return create_response(
                success=False,
                message="获取活跃任务失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/retry_task", response={200: BaseResponseSchema[Dict]})
    def retry_task(self, request: HttpRequest, task_id: str):
        """重试任务"""
        try:
            result = AsyncResult(task_id, app=celery_app)
            
            if result.failed():
                # 重新执行任务
                new_result = result.retry()
                
                return ZhiResponse(
                    data={
                        'original_task_id': task_id,
                        'new_task_id': new_result.id,
                        'status': 'retried'
                    },
                    message="任务重试成功"
                )
            else:
                return create_response(
                    success=False,
                    message="只能重试失败的任务",
                    data={'error': 'task_not_failed'},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"任务重试失败: {e}")
            return create_response(
                success=False,
                message="任务重试失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/revoke_task", response={200: BaseResponseSchema[Dict]})
    def revoke_task(self, request: HttpRequest, task_id: str, terminate: bool = True):
        """撤销任务"""
        try:
            celery_app.control.revoke(task_id, terminate=terminate)
            
            return ZhiResponse(
                data={
                    'task_id': task_id,
                    'terminated': terminate,
                    'status': 'revoked'
                },
                message="任务撤销成功"
            )
            
        except Exception as e:
            logger.error(f"任务撤销失败: {e}")
            return create_response(
                success=False,
                message="任务撤销失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
