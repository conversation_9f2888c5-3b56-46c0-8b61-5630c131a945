"""
ZhiCelery APIs - 使用自动CRUD简化开发
参考 simple_example_product 的设计模式
"""

from typing import List, Any, Dict
from ninja import ModelSchema, Field
from ninja_extra import http_get, http_post
from django.http import HttpRequest
from celery.result import AsyncResult
from celery import current_app

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_logger import get_logger

from ..models import CeleryTaskLog, CeleryWorkerStatus, CeleryQueueStats, CeleryScheduleTask
from ..celery_app import app as celery_app

logger = get_logger(__name__)


# ==================== Schema定义 ====================

class CeleryTaskLogSchemaIn(ModelSchema):
    """任务日志输入Schema"""
    class Meta:
        model = CeleryTaskLog
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryTaskLogSchemaOut(ModelSchema):
    """任务日志输出Schema"""
    class Meta:
        model = CeleryTaskLog
        fields = '__all__'


class CeleryWorkerStatusSchemaIn(ModelSchema):
    """Worker状态输入Schema"""
    class Meta:
        model = CeleryWorkerStatus
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryWorkerStatusSchemaOut(ModelSchema):
    """Worker状态输出Schema"""
    class Meta:
        model = CeleryWorkerStatus
        fields = '__all__'


class CeleryQueueStatsSchemaIn(ModelSchema):
    """队列统计输入Schema"""
    class Meta:
        model = CeleryQueueStats
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryQueueStatsSchemaOut(ModelSchema):
    """队列统计输出Schema"""
    class Meta:
        model = CeleryQueueStats
        fields = '__all__'


class CeleryScheduleTaskSchemaIn(ModelSchema):
    """定时任务输入Schema"""
    class Meta:
        model = CeleryScheduleTask
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryScheduleTaskSchemaOut(ModelSchema):
    """定时任务输出Schema"""
    class Meta:
        model = CeleryScheduleTask
        fields = '__all__'


# ==================== 控制器定义 ====================

@auto_crud_api(
    CeleryTaskLog,
    prefix="celery_task_logs",
    tags=["Celery任务日志"],
    schema_in=CeleryTaskLogSchemaIn,
    schema_out=CeleryTaskLogSchemaOut,
    exclude=[]
)
class CeleryTaskLogControllerAPI(BaseModelService):
    """Celery任务日志管理 - 自动CRUD API"""
    model = CeleryTaskLog
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/active_tasks", response={200: BaseResponseSchema[List[Dict]]})
    def get_active_tasks(self, request: HttpRequest):
        """获取当前活跃任务"""
        try:
            inspect = celery_app.control.inspect()
            active_tasks = inspect.active() or {}
            
            tasks = []
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['id'],
                        'name': task['name'],
                        'args': task['args'],
                        'kwargs': task['kwargs'],
                        'worker': worker,
                        'time_start': task.get('time_start'),
                    })
            
            return ZhiResponse(
                data=tasks,
                message=f"获取活跃任务成功，共{len(tasks)}个任务"
            )
            
        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return create_response(
                success=False,
                message="获取活跃任务失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/retry_task", response={200: BaseResponseSchema[Dict]})
    def retry_task(self, request: HttpRequest, task_id: str):
        """重试任务"""
        try:
            result = AsyncResult(task_id, app=celery_app)
            
            if result.failed():
                # 重新执行任务
                new_result = result.retry()
                
                return ZhiResponse(
                    data={
                        'original_task_id': task_id,
                        'new_task_id': new_result.id,
                        'status': 'retried'
                    },
                    message="任务重试成功"
                )
            else:
                return create_response(
                    success=False,
                    message="只能重试失败的任务",
                    data={'error': 'task_not_failed'},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"任务重试失败: {e}")
            return create_response(
                success=False,
                message="任务重试失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )


@auto_crud_api(
    CeleryWorkerStatus,
    prefix="celery_workers",
    tags=["Celery Worker状态"],
    schema_in=CeleryWorkerStatusSchemaIn,
    schema_out=CeleryWorkerStatusSchemaOut,
    exclude=[]
)
class CeleryWorkerStatusControllerAPI(BaseModelService):
    """Celery Worker状态管理 - 自动CRUD API"""
    model = CeleryWorkerStatus
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/live_status", response={200: BaseResponseSchema[List[Dict]]})
    def get_live_worker_status(self, request: HttpRequest):
        """获取实时Worker状态"""
        try:
            inspect = celery_app.control.inspect()
            
            # 获取Worker统计信息
            stats = inspect.stats() or {}
            active = inspect.active() or {}
            
            workers = []
            for worker_name, worker_stats in stats.items():
                worker_info = {
                    'name': worker_name,
                    'status': 'online' if worker_name in active else 'offline',
                    'active_tasks': len(active.get(worker_name, [])),
                    'processed_tasks': worker_stats.get('total', {}).get('tasks.processed', 0),
                    'pool': worker_stats.get('pool', {}).get('implementation', 'unknown'),
                    'concurrency': worker_stats.get('pool', {}).get('max-concurrency', 0),
                    'load_avg': worker_stats.get('rusage', {}).get('utime', 0),
                }
                workers.append(worker_info)
            
            return ZhiResponse(
                data=workers,
                message=f"获取Worker状态成功，共{len(workers)}个Worker"
            )
            
        except Exception as e:
            logger.error(f"获取Worker状态失败: {e}")
            return create_response(
                success=False,
                message="获取Worker状态失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )


@auto_crud_api(
    CeleryQueueStats,
    prefix="celery_queues",
    tags=["Celery队列统计"],
    schema_in=CeleryQueueStatsSchemaIn,
    schema_out=CeleryQueueStatsSchemaOut,
    exclude=[]
)
class CeleryQueueStatsControllerAPI(BaseModelService):
    """Celery队列统计管理 - 自动CRUD API"""
    model = CeleryQueueStats
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/queue_info", response={200: BaseResponseSchema[List[Dict]]})
    def get_queue_info(self, request: HttpRequest):
        """获取队列信息"""
        try:
            from django.conf import settings
            
            # 从配置中获取队列信息
            queues = getattr(settings, 'CELERY_TASK_QUEUES', [])
            
            queue_info = []
            for queue in queues:
                queue_name = queue.name if hasattr(queue, 'name') else str(queue)
                
                queue_info.append({
                    'name': queue_name,
                    'routing_key': getattr(queue, 'routing_key', queue_name),
                    'messages': 0,  # 需要从消息代理获取
                    'consumers': 0,  # 需要从消息代理获取
                })
            
            return ZhiResponse(
                data=queue_info,
                message=f"获取队列信息成功，共{len(queue_info)}个队列"
            )
            
        except Exception as e:
            logger.error(f"获取队列信息失败: {e}")
            return create_response(
                success=False,
                message="获取队列信息失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )


@auto_crud_api(
    CeleryScheduleTask,
    prefix="celery_schedules",
    tags=["Celery定时任务"],
    schema_in=CeleryScheduleTaskSchemaIn,
    schema_out=CeleryScheduleTaskSchemaOut,
    exclude=[]
)
class CeleryScheduleTaskControllerAPI(BaseModelService):
    """Celery定时任务管理 - 自动CRUD API"""
    model = CeleryScheduleTask
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_post, "/run_now", response={200: BaseResponseSchema[Dict]})
    def run_schedule_now(self, request: HttpRequest, schedule_id: int):
        """立即执行定时任务"""
        try:
            schedule = CeleryScheduleTask.objects.get(id=schedule_id, is_deleted=False)
            
            # 执行任务
            result = celery_app.send_task(
                schedule.task,
                args=schedule.args,
                kwargs=schedule.kwargs,
                queue=schedule.queue
            )
            
            # 更新运行统计
            schedule.total_run_count += 1
            schedule.last_run_at = timezone.now()
            schedule.save()
            
            return ZhiResponse(
                data={
                    'task_id': result.id,
                    'schedule_id': schedule_id,
                    'task_name': schedule.task,
                    'status': 'PENDING'
                },
                message="定时任务执行成功"
            )
            
        except CeleryScheduleTask.DoesNotExist:
            return create_response(
                success=False,
                message="定时任务不存在",
                data={'error': 'schedule_not_found'},
                code=ResponseCode.NOT_FOUND,
                status_code=404
            )
        except Exception as e:
            logger.error(f"执行定时任务失败: {e}")
            return create_response(
                success=False,
                message="执行定时任务失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
