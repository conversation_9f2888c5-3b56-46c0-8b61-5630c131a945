"""
Celery任务管理控制器
参考 zhi_oauth 的控制器设计模式
"""

from typing import Optional, List
from ninja_extra import api_controller, http_get, http_post
from ninja import Schema, Query
from django.http import HttpRequest
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from ..services import celery_management_service
from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_response.base import create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode

logger = get_logger(module_name="celery_management_controller")


# ==================== Schema定义 ====================

class TaskListQuerySchema(Schema):
    """任务列表查询参数"""
    status: Optional[str] = None
    limit: int = 100
    offset: int = 0
    queue: Optional[str] = None


class TaskRetrySchema(Schema):
    """任务重试请求"""
    task_id: str
    force: bool = False


class TaskRevokeSchema(Schema):
    """任务撤销请求"""
    task_id: str
    terminate: bool = True
    signal: str = 'SIGTERM'


class TaskExecuteSchema(Schema):
    """任务执行请求"""
    task_name: str
    args: List = []
    kwargs: dict = {}
    queue: Optional[str] = None
    countdown: Optional[int] = None
    eta: Optional[str] = None


# ==================== 控制器定义 ====================

@api_controller(tags=['任务管理'])
class CeleryManagementControllerAPI:
    """Celery任务管理控制器"""
    
    @http_get('/tasks')
    def get_task_list(self, request: HttpRequest, query: TaskListQuerySchema = Query(...)):
        """
        获取任务列表
        
        支持按状态、队列过滤，分页查询
        """
        try:
            result = celery_management_service.get_task_list(
                status_filter=query.status,
                limit=query.limit
            )
            
            if result['success']:
                # 应用分页
                tasks = result['data']['tasks']
                total = len(tasks)
                
                # 简单分页处理
                start = query.offset
                end = start + query.limit
                paginated_tasks = tasks[start:end]
                
                return create_response(
                    data={
                        'tasks': paginated_tasks,
                        'pagination': {
                            'total': total,
                            'limit': query.limit,
                            'offset': query.offset,
                            'has_next': end < total,
                            'has_prev': start > 0
                        },
                        'stats': result['data']['stats']
                    },
                    message='获取任务列表成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取任务列表异常: {e}")
            return create_response(
                success=False,
                message='获取任务列表失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/tasks/{task_id}')
    def get_task_detail(self, request: HttpRequest, task_id: str):
        """
        获取任务详情
        
        Args:
            task_id: 任务ID
        """
        try:
            result = celery_management_service.get_task_detail(task_id)
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取任务详情成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"获取任务详情异常: {e}")
            return create_response(
                success=False,
                message='获取任务详情失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/tasks/retry')
    def retry_task(self, request: HttpRequest, data: TaskRetrySchema):
        """
        重试任务
        
        重新执行失败的任务
        """
        try:
            result = celery_management_service.retry_task(data.task_id)
            
            if result['success']:
                logger.info(f"任务重试成功: {data.task_id}")
                return create_response(
                    data=result['data'],
                    message=result['message']
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"任务重试异常: {e}")
            return create_response(
                success=False,
                message='任务重试失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/tasks/revoke')
    def revoke_task(self, request: HttpRequest, data: TaskRevokeSchema):
        """
        撤销任务
        
        取消正在执行或等待执行的任务
        """
        try:
            result = celery_management_service.revoke_task(
                task_id=data.task_id,
                terminate=data.terminate
            )
            
            if result['success']:
                logger.info(f"任务撤销成功: {data.task_id}")
                return create_response(
                    data=result['data'],
                    message=result['message']
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"任务撤销异常: {e}")
            return create_response(
                success=False,
                message='任务撤销失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/tasks/execute')
    def execute_task(self, request: HttpRequest, data: TaskExecuteSchema):
        """
        执行任务
        
        手动触发任务执行
        """
        try:
            from ..celery_app import app as celery_app
            from datetime import datetime
            
            # 构建任务参数
            task_kwargs = {
                'args': data.args,
                'kwargs': data.kwargs,
            }
            
            if data.queue:
                task_kwargs['queue'] = data.queue
            
            if data.countdown:
                task_kwargs['countdown'] = data.countdown
            
            if data.eta:
                # 解析ETA时间
                try:
                    eta_time = datetime.fromisoformat(data.eta.replace('Z', '+00:00'))
                    task_kwargs['eta'] = eta_time
                except ValueError:
                    return create_response(
                        success=False,
                        message='ETA时间格式错误',
                        data={'error': 'invalid_eta_format'},
                        code=ResponseCode.BAD_REQUEST,
                        status_code=400
                    )
            
            # 执行任务
            result = celery_app.send_task(data.task_name, **task_kwargs)
            
            logger.info(f"手动执行任务: {data.task_name}, task_id: {result.id}")
            
            return create_response(
                data={
                    'task_id': result.id,
                    'task_name': data.task_name,
                    'status': 'PENDING',
                    'queue': data.queue or 'default'
                },
                message='任务执行成功'
            )
            
        except Exception as e:
            logger.error(f"任务执行异常: {e}")
            return create_response(
                success=False,
                message='任务执行失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
