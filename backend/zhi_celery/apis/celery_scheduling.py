"""
Celery调度控制器
提供定时任务管理功能
"""

from typing import Optional
from ninja_extra import api_controller, http_get, http_post, http_put, http_delete
from ninja import Schema
from django.http import HttpRequest
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from ..services import celery_management_service
from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_response.base import create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode

logger = get_logger(module_name="celery_scheduling_controller")


# ==================== Schema定义 ====================

class ScheduleCreateSchema(Schema):
    """创建定时任务请求"""
    name: str
    task: str
    schedule_type: str  # 'crontab' or 'interval'
    
    # Crontab参数
    minute: Optional[str] = None
    hour: Optional[str] = None
    day_of_week: Optional[str] = None
    day_of_month: Optional[str] = None
    month_of_year: Optional[str] = None
    
    # Interval参数
    every: Optional[int] = None
    period: Optional[str] = None  # 'seconds', 'minutes', 'hours', 'days'
    
    # 任务参数
    args: list = []
    kwargs: dict = {}
    queue: Optional[str] = None
    enabled: bool = True
    description: Optional[str] = None


class ScheduleUpdateSchema(Schema):
    """更新定时任务请求"""
    name: Optional[str] = None
    enabled: Optional[bool] = None
    description: Optional[str] = None
    
    # Crontab参数
    minute: Optional[str] = None
    hour: Optional[str] = None
    day_of_week: Optional[str] = None
    day_of_month: Optional[str] = None
    month_of_year: Optional[str] = None
    
    # Interval参数
    every: Optional[int] = None
    period: Optional[str] = None
    
    # 任务参数
    args: Optional[list] = None
    kwargs: Optional[dict] = None
    queue: Optional[str] = None


# ==================== 控制器定义 ====================

@api_controller(tags=['任务调度'])
class CelerySchedulingControllerAPI:
    """Celery调度控制器"""
    
    @http_get('/schedules')
    def get_schedule_list(self, request: HttpRequest):
        """
        获取定时任务列表
        """
        try:
            result = celery_management_service.get_schedule_list()
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取定时任务列表成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取定时任务列表异常: {e}")
            return create_response(
                success=False,
                message='获取定时任务列表失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/schedules/{schedule_id}')
    def get_schedule_detail(self, request: HttpRequest, schedule_id: int):
        """
        获取定时任务详情
        """
        try:
            result = celery_management_service.get_schedule_detail(schedule_id)
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取定时任务详情成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"获取定时任务详情异常: {e}")
            return create_response(
                success=False,
                message='获取定时任务详情失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/schedules')
    def create_schedule(self, request: HttpRequest, data: ScheduleCreateSchema):
        """
        创建定时任务
        """
        try:
            result = celery_management_service.create_schedule(
                name=data.name,
                task=data.task,
                schedule_type=data.schedule_type,
                schedule_params={
                    'minute': data.minute,
                    'hour': data.hour,
                    'day_of_week': data.day_of_week,
                    'day_of_month': data.day_of_month,
                    'month_of_year': data.month_of_year,
                    'every': data.every,
                    'period': data.period,
                },
                task_params={
                    'args': data.args,
                    'kwargs': data.kwargs,
                    'queue': data.queue,
                },
                enabled=data.enabled,
                description=data.description
            )
            
            if result['success']:
                logger.info(f"创建定时任务成功: {data.name}")
                return create_response(
                    data=result['data'],
                    message='创建定时任务成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"创建定时任务异常: {e}")
            return create_response(
                success=False,
                message='创建定时任务失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_put('/schedules/{schedule_id}')
    def update_schedule(self, request: HttpRequest, schedule_id: int, data: ScheduleUpdateSchema):
        """
        更新定时任务
        """
        try:
            result = celery_management_service.update_schedule(
                schedule_id=schedule_id,
                update_data=data.dict(exclude_unset=True)
            )
            
            if result['success']:
                logger.info(f"更新定时任务成功: {schedule_id}")
                return create_response(
                    data=result['data'],
                    message='更新定时任务成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
                
        except Exception as e:
            logger.error(f"更新定时任务异常: {e}")
            return create_response(
                success=False,
                message='更新定时任务失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_delete('/schedules/{schedule_id}')
    def delete_schedule(self, request: HttpRequest, schedule_id: int):
        """
        删除定时任务
        """
        try:
            result = celery_management_service.delete_schedule(schedule_id)
            
            if result['success']:
                logger.info(f"删除定时任务成功: {schedule_id}")
                return create_response(
                    data=result['data'],
                    message='删除定时任务成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"删除定时任务异常: {e}")
            return create_response(
                success=False,
                message='删除定时任务失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/schedules/{schedule_id}/toggle')
    def toggle_schedule(self, request: HttpRequest, schedule_id: int):
        """
        启用/禁用定时任务
        """
        try:
            result = celery_management_service.toggle_schedule(schedule_id)
            
            if result['success']:
                logger.info(f"切换定时任务状态成功: {schedule_id}")
                return create_response(
                    data=result['data'],
                    message='切换定时任务状态成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"切换定时任务状态异常: {e}")
            return create_response(
                success=False,
                message='切换定时任务状态失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/schedules/{schedule_id}/run')
    def run_schedule_now(self, request: HttpRequest, schedule_id: int):
        """
        立即执行定时任务
        """
        try:
            result = celery_management_service.run_schedule_now(schedule_id)
            
            if result['success']:
                logger.info(f"立即执行定时任务成功: {schedule_id}")
                return create_response(
                    data=result['data'],
                    message='立即执行定时任务成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"立即执行定时任务异常: {e}")
            return create_response(
                success=False,
                message='立即执行定时任务失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
