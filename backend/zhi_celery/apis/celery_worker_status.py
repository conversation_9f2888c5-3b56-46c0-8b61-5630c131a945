"""
CeleryWorkerStatus API
Celery Worker状态管理接口
"""

from typing import List, Dict
from ninja import ModelSchema
from ninja_extra import http_get, http_post
from django.http import HttpRequest

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_logger import get_logger

from ..models import CeleryWorkerStatus
from ..celery_app import app as celery_app

logger = get_logger(__name__)


class CeleryWorkerStatusSchemaIn(ModelSchema):
    """Worker状态输入Schema"""
    class Meta:
        model = CeleryWorkerStatus
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryWorkerStatusSchemaOut(ModelSchema):
    """Worker状态输出Schema"""
    class Meta:
        model = CeleryWorkerStatus
        fields = '__all__'


@auto_crud_api(
    CeleryWorkerStatus,
    prefix="celery_workers",
    tags=["Celery Worker状态"],
    schema_in=CeleryWorkerStatusSchemaIn,
    schema_out=CeleryWorkerStatusSchemaOut,
    exclude=[]
)
class CeleryWorkerStatusControllerAPI(BaseModelService):
    """Celery Worker状态管理 - 自动CRUD API"""
    model = CeleryWorkerStatus
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/live_status", response={200: BaseResponseSchema[List[Dict]]})
    def get_live_worker_status(self, request: HttpRequest):
        """获取实时Worker状态"""
        try:
            inspect = celery_app.control.inspect()
            
            # 获取Worker统计信息
            stats = inspect.stats() or {}
            active = inspect.active() or {}
            
            workers = []
            for worker_name, worker_stats in stats.items():
                worker_info = {
                    'name': worker_name,
                    'status': 'online' if worker_name in active else 'offline',
                    'active_tasks': len(active.get(worker_name, [])),
                    'processed_tasks': worker_stats.get('total', {}).get('tasks.processed', 0),
                    'pool': worker_stats.get('pool', {}).get('implementation', 'unknown'),
                    'concurrency': worker_stats.get('pool', {}).get('max-concurrency', 0),
                    'load_avg': worker_stats.get('rusage', {}).get('utime', 0),
                    'hostname': worker_stats.get('hostname', 'unknown'),
                }
                workers.append(worker_info)
            
            return ZhiResponse(
                data=workers,
                message=f"获取Worker状态成功，共{len(workers)}个Worker"
            )
            
        except Exception as e:
            logger.error(f"获取Worker状态失败: {e}")
            return create_response(
                success=False,
                message="获取Worker状态失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_get, "/worker_stats", response={200: BaseResponseSchema[Dict]})
    def get_worker_stats(self, request: HttpRequest, worker_name: str):
        """获取指定Worker的详细统计"""
        try:
            inspect = celery_app.control.inspect([worker_name])
            
            stats = inspect.stats() or {}
            active = inspect.active() or {}
            reserved = inspect.reserved() or {}
            
            worker_stats = stats.get(worker_name, {})
            
            if not worker_stats:
                return create_response(
                    success=False,
                    message="Worker不存在或离线",
                    data={'error': 'worker_not_found'},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
            
            return ZhiResponse(
                data={
                    'name': worker_name,
                    'stats': worker_stats,
                    'active_tasks': active.get(worker_name, []),
                    'reserved_tasks': reserved.get(worker_name, []),
                    'active_count': len(active.get(worker_name, [])),
                    'reserved_count': len(reserved.get(worker_name, [])),
                },
                message="获取Worker统计成功"
            )
            
        except Exception as e:
            logger.error(f"获取Worker统计失败: {e}")
            return create_response(
                success=False,
                message="获取Worker统计失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/shutdown_worker", response={200: BaseResponseSchema[Dict]})
    def shutdown_worker(self, request: HttpRequest, worker_name: str):
        """关闭指定Worker"""
        try:
            celery_app.control.shutdown([worker_name])
            
            return ZhiResponse(
                data={
                    'worker_name': worker_name,
                    'action': 'shutdown',
                    'status': 'requested'
                },
                message=f"Worker {worker_name} 关闭请求已发送"
            )
            
        except Exception as e:
            logger.error(f"关闭Worker失败: {e}")
            return create_response(
                success=False,
                message="关闭Worker失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
