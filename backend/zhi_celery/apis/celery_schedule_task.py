"""
CeleryScheduleTask API
Celery定时任务管理接口
"""

from typing import List, Dict
from ninja import ModelSchema
from ninja_extra import http_get, http_post
from django.http import HttpRequest
from django.utils import timezone

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_logger import get_logger

from ..models import CeleryScheduleTask
from ..celery_app import app as celery_app

logger = get_logger(__name__)


class CeleryScheduleTaskSchemaIn(ModelSchema):
    """定时任务输入Schema"""
    class Meta:
        model = CeleryScheduleTask
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryScheduleTaskSchemaOut(ModelSchema):
    """定时任务输出Schema"""
    class Meta:
        model = CeleryScheduleTask
        fields = '__all__'


@auto_crud_api(
    CeleryScheduleTask,
    prefix="celery_schedules",
    tags=["Celery定时任务"],
    schema_in=CeleryScheduleTaskSchemaIn,
    schema_out=CeleryScheduleTaskSchemaOut,
    exclude=[]
)
class CeleryScheduleTaskControllerAPI(BaseModelService):
    """Celery定时任务管理 - 自动CRUD API"""
    model = CeleryScheduleTask
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_post, "/run_now", response={200: BaseResponseSchema[Dict]})
    def run_schedule_now(self, request: HttpRequest, schedule_id: int):
        """立即执行定时任务"""
        try:
            schedule = CeleryScheduleTask.objects.get(id=schedule_id, is_deleted=False)
            
            # 执行任务
            result = celery_app.send_task(
                schedule.task,
                args=schedule.args,
                kwargs=schedule.kwargs,
                queue=schedule.queue
            )
            
            # 更新运行统计
            schedule.total_run_count += 1
            schedule.last_run_at = timezone.now()
            schedule.save()
            
            return ZhiResponse(
                data={
                    'task_id': result.id,
                    'schedule_id': schedule_id,
                    'task_name': schedule.task,
                    'status': 'PENDING',
                    'queue': schedule.queue
                },
                message="定时任务执行成功"
            )
            
        except CeleryScheduleTask.DoesNotExist:
            return create_response(
                success=False,
                message="定时任务不存在",
                data={'error': 'schedule_not_found'},
                code=ResponseCode.NOT_FOUND,
                status_code=404
            )
        except Exception as e:
            logger.error(f"执行定时任务失败: {e}")
            return create_response(
                success=False,
                message="执行定时任务失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/toggle_schedule", response={200: BaseResponseSchema[Dict]})
    def toggle_schedule(self, request: HttpRequest, schedule_id: int):
        """启用/禁用定时任务"""
        try:
            schedule = CeleryScheduleTask.objects.get(id=schedule_id, is_deleted=False)
            
            # 切换状态
            schedule.enabled = not schedule.enabled
            schedule.save()
            
            return ZhiResponse(
                data={
                    'schedule_id': schedule_id,
                    'name': schedule.name,
                    'enabled': schedule.enabled,
                    'status': 'enabled' if schedule.enabled else 'disabled'
                },
                message=f"定时任务已{'启用' if schedule.enabled else '禁用'}"
            )
            
        except CeleryScheduleTask.DoesNotExist:
            return create_response(
                success=False,
                message="定时任务不存在",
                data={'error': 'schedule_not_found'},
                code=ResponseCode.NOT_FOUND,
                status_code=404
            )
        except Exception as e:
            logger.error(f"切换定时任务状态失败: {e}")
            return create_response(
                success=False,
                message="切换定时任务状态失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_get, "/schedule_history", response={200: BaseResponseSchema[List[Dict]]})
    def get_schedule_history(self, request: HttpRequest, schedule_id: int, limit: int = 10):
        """获取定时任务执行历史"""
        try:
            schedule = CeleryScheduleTask.objects.get(id=schedule_id, is_deleted=False)
            
            # 从任务日志中获取执行历史
            from ..models import CeleryTaskLog
            
            history = CeleryTaskLog.objects.filter(
                task_name=schedule.task,
                is_deleted=False
            ).order_by('-created_at')[:limit]
            
            history_data = []
            for log in history:
                history_data.append({
                    'task_id': log.task_id,
                    'status': log.status,
                    'started_at': log.started_at.isoformat() if log.started_at else None,
                    'completed_at': log.completed_at.isoformat() if log.completed_at else None,
                    'runtime': log.runtime,
                    'result': log.result,
                    'worker': log.worker,
                })
            
            return ZhiResponse(
                data={
                    'schedule_id': schedule_id,
                    'schedule_name': schedule.name,
                    'total_run_count': schedule.total_run_count,
                    'last_run_at': schedule.last_run_at.isoformat() if schedule.last_run_at else None,
                    'history': history_data
                },
                message=f"获取定时任务执行历史成功，共{len(history_data)}条记录"
            )
            
        except CeleryScheduleTask.DoesNotExist:
            return create_response(
                success=False,
                message="定时任务不存在",
                data={'error': 'schedule_not_found'},
                code=ResponseCode.NOT_FOUND,
                status_code=404
            )
        except Exception as e:
            logger.error(f"获取定时任务执行历史失败: {e}")
            return create_response(
                success=False,
                message="获取定时任务执行历史失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_get, "/validate_cron", response={200: BaseResponseSchema[Dict]})
    def validate_cron_expression(self, request: HttpRequest, expression: str):
        """验证Cron表达式"""
        try:
            from celery.schedules import crontab
            
            # 解析Cron表达式
            parts = expression.split()
            if len(parts) != 5:
                return create_response(
                    success=False,
                    message="Cron表达式格式错误，应为5个部分：分 时 日 月 周",
                    data={'error': 'invalid_format'},
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
            
            minute, hour, day, month, day_of_week = parts
            
            # 创建crontab对象验证
            cron = crontab(
                minute=minute,
                hour=hour,
                day_of_month=day,
                month_of_year=month,
                day_of_week=day_of_week
            )
            
            return ZhiResponse(
                data={
                    'expression': expression,
                    'valid': True,
                    'parsed': {
                        'minute': minute,
                        'hour': hour,
                        'day_of_month': day,
                        'month_of_year': month,
                        'day_of_week': day_of_week
                    }
                },
                message="Cron表达式验证成功"
            )
            
        except Exception as e:
            logger.error(f"Cron表达式验证失败: {e}")
            return create_response(
                success=False,
                message="Cron表达式验证失败",
                data={'error': str(e), 'valid': False},
                code=ResponseCode.BAD_REQUEST,
                status_code=400
            )
