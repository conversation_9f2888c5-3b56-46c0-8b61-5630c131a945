"""
Celery监控控制器
提供Worker、队列、系统状态监控功能
"""

from typing import Optional
from ninja_extra import api_controller, http_get
from ninja import Schema, Query
from django.http import HttpRequest

from ..services import celery_management_service
from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_response.base import create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode

logger = get_logger(module_name="celery_monitoring_controller")


# ==================== Schema定义 ====================

class MonitoringQuerySchema(Schema):
    """监控查询参数"""
    refresh: bool = False
    include_stats: bool = True


# ==================== 控制器定义 ====================

@api_controller(tags=['系统监控'])
class CeleryMonitoringControllerAPI:
    """Celery监控控制器"""
    
    @http_get('/workers')
    def get_worker_list(self, request: HttpRequest, query: MonitoringQuerySchema = Query(...)):
        """
        获取Worker列表和状态
        """
        try:
            result = celery_management_service.get_worker_list(
                include_stats=query.include_stats
            )
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取Worker列表成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取Worker列表异常: {e}")
            return create_response(
                success=False,
                message='获取Worker列表失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/workers/{worker_name}/stats')
    def get_worker_stats(self, request: HttpRequest, worker_name: str):
        """
        获取指定Worker的详细统计信息
        """
        try:
            result = celery_management_service.get_worker_stats(worker_name)
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取Worker统计成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"获取Worker统计异常: {e}")
            return create_response(
                success=False,
                message='获取Worker统计失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/queues')
    def get_queue_list(self, request: HttpRequest):
        """
        获取队列列表和状态
        """
        try:
            result = celery_management_service.get_queue_list()
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取队列列表成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取队列列表异常: {e}")
            return create_response(
                success=False,
                message='获取队列列表失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/queues/{queue_name}/stats')
    def get_queue_stats(self, request: HttpRequest, queue_name: str):
        """
        获取指定队列的详细统计信息
        """
        try:
            result = celery_management_service.get_queue_stats(queue_name)
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取队列统计成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.NOT_FOUND,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"获取队列统计异常: {e}")
            return create_response(
                success=False,
                message='获取队列统计失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/system/health')
    def get_system_health(self, request: HttpRequest):
        """
        获取Celery系统健康状态
        """
        try:
            result = celery_management_service.get_system_health()
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取系统健康状态成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取系统健康状态异常: {e}")
            return create_response(
                success=False,
                message='获取系统健康状态失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/system/stats')
    def get_system_stats(self, request: HttpRequest):
        """
        获取Celery系统统计信息
        """
        try:
            result = celery_management_service.get_system_stats()
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取系统统计成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取系统统计异常: {e}")
            return create_response(
                success=False,
                message='获取系统统计失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/dashboard')
    def get_dashboard_data(self, request: HttpRequest):
        """
        获取仪表板数据
        
        包含系统概览、任务统计、Worker状态等综合信息
        """
        try:
            result = celery_management_service.get_dashboard_data()
            
            if result['success']:
                return create_response(
                    data=result['data'],
                    message='获取仪表板数据成功'
                )
            else:
                return create_response(
                    success=False,
                    message=result['message'],
                    data={'error': result['error']},
                    code=ResponseCode.INTERNAL_ERROR,
                    status_code=500
                )
                
        except Exception as e:
            logger.error(f"获取仪表板数据异常: {e}")
            return create_response(
                success=False,
                message='获取仪表板数据失败',
                data={'error': 'server_error'},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
