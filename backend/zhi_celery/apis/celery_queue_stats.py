"""
CeleryQueueStats API
Celery队列统计管理接口
"""

from typing import List, Dict
from ninja import ModelSchema
from ninja_extra import http_get, http_post
from django.http import HttpRequest

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_logger import get_logger

from ..models import CeleryQueueStats
from ..celery_app import app as celery_app

logger = get_logger(__name__)


class CeleryQueueStatsSchemaIn(ModelSchema):
    """队列统计输入Schema"""
    class Meta:
        model = CeleryQueueStats
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class CeleryQueueStatsSchemaOut(ModelSchema):
    """队列统计输出Schema"""
    class Meta:
        model = CeleryQueueStats
        fields = '__all__'


@auto_crud_api(
    CeleryQueueStats,
    prefix="celery_queues",
    tags=["Celery队列统计"],
    schema_in=CeleryQueueStatsSchemaIn,
    schema_out=CeleryQueueStatsSchemaOut,
    exclude=[]
)
class CeleryQueueStatsControllerAPI(BaseModelService):
    """Celery队列统计管理 - 自动CRUD API"""
    model = CeleryQueueStats
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    @api_route(http_get, "/queue_info", response={200: BaseResponseSchema[List[Dict]]})
    def get_queue_info(self, request: HttpRequest):
        """获取队列信息"""
        try:
            from django.conf import settings
            
            # 从配置中获取队列信息
            queues = getattr(settings, 'CELERY_TASK_QUEUES', [])
            
            queue_info = []
            for queue in queues:
                queue_name = queue.name if hasattr(queue, 'name') else str(queue)
                
                queue_info.append({
                    'name': queue_name,
                    'routing_key': getattr(queue, 'routing_key', queue_name),
                    'messages': 0,  # 需要从消息代理获取
                    'consumers': 0,  # 需要从消息代理获取
                    'exchange': getattr(queue, 'exchange', None),
                })
            
            return ZhiResponse(
                data=queue_info,
                message=f"获取队列信息成功，共{len(queue_info)}个队列"
            )
            
        except Exception as e:
            logger.error(f"获取队列信息失败: {e}")
            return create_response(
                success=False,
                message="获取队列信息失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_get, "/queue_length", response={200: BaseResponseSchema[Dict]})
    def get_queue_length(self, request: HttpRequest, queue_name: str):
        """获取队列长度"""
        try:
            # 这里需要根据您使用的消息代理（Redis/RabbitMQ）来实现
            # 以下是示例实现
            
            inspect = celery_app.control.inspect()
            reserved = inspect.reserved() or {}
            scheduled = inspect.scheduled() or {}
            
            # 统计指定队列的任务数量
            queue_tasks = 0
            for worker_tasks in reserved.values():
                queue_tasks += len([t for t in worker_tasks if t.get('delivery_info', {}).get('routing_key') == queue_name])
            
            for worker_tasks in scheduled.values():
                queue_tasks += len([t for t in worker_tasks if t.get('delivery_info', {}).get('routing_key') == queue_name])
            
            return ZhiResponse(
                data={
                    'queue_name': queue_name,
                    'length': queue_tasks,
                    'reserved_tasks': sum(len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == queue_name]) for tasks in reserved.values()),
                    'scheduled_tasks': sum(len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == queue_name]) for tasks in scheduled.values()),
                },
                message=f"获取队列 {queue_name} 长度成功"
            )
            
        except Exception as e:
            logger.error(f"获取队列长度失败: {e}")
            return create_response(
                success=False,
                message="获取队列长度失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )

    @api_route(http_post, "/purge_queue", response={200: BaseResponseSchema[Dict]})
    def purge_queue(self, request: HttpRequest, queue_name: str):
        """清空队列"""
        try:
            # 清空指定队列
            celery_app.control.purge()
            
            return ZhiResponse(
                data={
                    'queue_name': queue_name,
                    'action': 'purged',
                    'status': 'success'
                },
                message=f"队列 {queue_name} 清空成功"
            )
            
        except Exception as e:
            logger.error(f"清空队列失败: {e}")
            return create_response(
                success=False,
                message="清空队列失败",
                data={'error': str(e)},
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
