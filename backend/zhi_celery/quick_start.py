#!/usr/bin/env python3
"""
ZhiCelery 快速启动脚本
提供简单的交互式启动方式

使用方式:
python backend/zhi_celery/quick_start.py
"""

import os
import sys
import subprocess
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
backend_path = project_root / 'backend'
sys.path.insert(0, str(backend_path))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text, color=Colors.WHITE):
    """打印带颜色的文本"""
    print(f"{color}{text}{Colors.END}")

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 ZhiCelery 快速启动器                    ║
║                   异步任务管理子项目                          ║
╚══════════════════════════════════════════════════════════════╝
    """
    print_colored(banner, Colors.CYAN + Colors.BOLD)

def check_environment():
    """检查运行环境"""
    print_colored("🔍 检查运行环境...", Colors.BLUE)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print_colored("❌ Python版本过低，需要Python 3.8+", Colors.RED)
        return False
    
    print_colored(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}", Colors.GREEN)
    
    # 检查必要的包
    required_packages = ['django', 'celery', 'redis']
    for package in required_packages:
        try:
            __import__(package)
            print_colored(f"✅ {package}", Colors.GREEN)
        except ImportError:
            print_colored(f"❌ {package} 未安装", Colors.RED)
            return False
    
    # 检查Redis连接
    try:
        import redis
        r = redis.Redis()
        r.ping()
        print_colored("✅ Redis连接正常", Colors.GREEN)
    except Exception as e:
        print_colored(f"⚠️  Redis连接失败: {e}", Colors.YELLOW)
        print_colored("   请确保Redis服务正在运行", Colors.YELLOW)
    
    return True

def show_menu():
    """显示主菜单"""
    menu = """
┌─────────────────────────────────────────────────────────────┐
│                        🎮 选择启动模式                        │
├─────────────────────────────────────────────────────────────┤
│  1. 🔄 启动Worker (处理异步任务)                             │
│  2. ⏰ 启动Beat (定时任务调度器)                             │
│  3. 🌸 启动Flower (任务监控界面)                            │
│  4. 🌐 启动Web管理服务                                      │
│  5. 🚀 启动所有服务 (推荐)                                  │
│  6. 📊 查看服务状态                                         │
│  7. 🛑 停止所有服务                                         │
│  8. ❓ 显示帮助信息                                         │
│  0. 🚪 退出                                                 │
└─────────────────────────────────────────────────────────────┘
    """
    print_colored(menu, Colors.WHITE)

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input(f"{Colors.YELLOW}请选择操作 (0-8): {Colors.END}").strip()
            if choice in ['0', '1', '2', '3', '4', '5', '6', '7', '8']:
                return choice
            else:
                print_colored("❌ 无效选择，请输入0-8之间的数字", Colors.RED)
        except KeyboardInterrupt:
            print_colored("\n👋 再见！", Colors.CYAN)
            sys.exit(0)

def run_command(cmd, cwd=None, background=False):
    """运行命令"""
    if cwd is None:
        cwd = backend_path
    
    print_colored(f"📋 执行命令: {' '.join(cmd)}", Colors.BLUE)
    
    if background:
        # 后台运行
        process = subprocess.Popen(
            cmd,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        return process
    else:
        # 前台运行
        try:
            subprocess.run(cmd, cwd=cwd, check=True)
        except KeyboardInterrupt:
            print_colored("\n🛑 用户中断操作", Colors.YELLOW)
        except subprocess.CalledProcessError as e:
            print_colored(f"❌ 命令执行失败: {e}", Colors.RED)

def start_worker():
    """启动Worker"""
    print_colored("🔄 启动Celery Worker...", Colors.GREEN)

    # 检查是否存在 celery_worker 管理命令
    try:
        # 先测试命令是否存在
        test_cmd = [sys.executable, 'manage.py', 'help', 'celery_worker']
        result = subprocess.run(test_cmd, cwd=backend_path, capture_output=True, text=True)

        if result.returncode != 0:
            print_colored("❌ celery_worker 管理命令不存在，尝试使用标准 celery 命令", Colors.YELLOW)
            # 使用标准 celery 命令
            cmd = [
                'celery', '-A', 'zhi_celery.celery_app', 'worker',
                '--loglevel=info',
                '--queues=default,logger,oauth,system',
                '--concurrency=2',
                '--pool=solo'
            ]

            # 设置环境变量
            env = os.environ.copy()
            env['DJANGO_SETTINGS_MODULE'] = 'application.settings.zhi_celery'

            try:
                subprocess.run(cmd, cwd=backend_path, env=env, check=True)
            except FileNotFoundError:
                print_colored("❌ 未找到 celery 命令，请确保已安装 celery", Colors.RED)
                return
        else:
            # 使用 Django 管理命令
            cmd = [
                sys.executable, 'manage.py', 'celery_worker',
                '--settings=application.settings.zhi_celery',
                '--pool=solo',
                '--loglevel=info',
                '--queues=default,logger,oauth,system',
                '--concurrency=2'
            ]
            run_command(cmd)

    except Exception as e:
        print_colored(f"❌ 启动 Worker 时出错: {e}", Colors.RED)

def start_beat():
    """启动Beat"""
    print_colored("⏰ 启动Celery Beat...", Colors.GREEN)

    # 检查是否存在 celery_beat 管理命令
    try:
        # 先测试命令是否存在
        test_cmd = [sys.executable, 'manage.py', 'help', 'celery_beat']
        result = subprocess.run(test_cmd, cwd=backend_path, capture_output=True, text=True)

        if result.returncode != 0:
            print_colored("❌ celery_beat 管理命令不存在，尝试使用标准 celery 命令", Colors.YELLOW)
            # 使用标准 celery 命令
            cmd = [
                'celery', '-A', 'zhi_celery.celery_app', 'beat',
                '--loglevel=info',
                '--scheduler=django_celery_beat.schedulers:DatabaseScheduler'
            ]

            # 设置环境变量
            env = os.environ.copy()
            env['DJANGO_SETTINGS_MODULE'] = 'application.settings.zhi_celery'

            try:
                subprocess.run(cmd, cwd=backend_path, env=env, check=True)
            except FileNotFoundError:
                print_colored("❌ 未找到 celery 命令，请确保已安装 celery", Colors.RED)
                return
        else:
            # 使用 Django 管理命令
            cmd = [
                sys.executable, 'manage.py', 'celery_beat',
                '--settings=application.settings.zhi_celery',
                '--loglevel=info'
            ]
            run_command(cmd)

    except Exception as e:
        print_colored(f"❌ 启动 Beat 时出错: {e}", Colors.RED)

def start_flower():
    """启动Flower"""
    print_colored("🌸 启动Flower监控...", Colors.GREEN)
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = 'application.settings.zhi_celery'
    
    cmd = [
        'celery', '-A', 'zhi_celery.celery_app', 'flower',
        '--port=5555',
        '--loglevel=info'
    ]
    
    try:
        subprocess.run(cmd, cwd=backend_path, env=env, check=True)
    except FileNotFoundError:
        print_colored("❌ 未找到celery命令，请确保已安装celery", Colors.RED)
    except KeyboardInterrupt:
        print_colored("\n🛑 Flower已停止", Colors.YELLOW)
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ Flower启动失败: {e}", Colors.RED)

def start_web():
    """启动Web服务"""
    print_colored("🌐 启动ZhiCelery Web管理服务...", Colors.GREEN)

    # 直接使用 Django runserver 命令
    cmd = [
        sys.executable, 'manage.py', 'runserver',
        '0.0.0.0:8004',
        '--settings=application.settings.zhi_celery'
    ]

    print_colored("🌐 ZhiCelery Web服务将在以下地址启动:", Colors.CYAN)
    print_colored("   • 管理界面: http://localhost:8004", Colors.BLUE)
    print_colored("   • API文档: http://localhost:8004/api/celery/docs/", Colors.BLUE)
    print_colored("   • 按 Ctrl+C 停止服务", Colors.YELLOW)

    run_command(cmd)

def start_all():
    """启动所有服务"""
    print_colored("🚀 启动所有ZhiCelery服务...", Colors.GREEN)
    
    # 创建日志目录
    log_dir = backend_path / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    processes = []
    
    try:
        # 启动Worker
        print_colored("启动Worker...", Colors.BLUE)
        worker_cmd = [
            sys.executable, 'manage.py', 'celery_worker',
            '--settings=application.settings.zhi_celery',
            '--pool=solo',
            '--loglevel=info',
            '--queues=default,logger,oauth,system',
            '--concurrency=2'
        ]
        worker_process = run_command(worker_cmd, background=True)
        processes.append(('Worker', worker_process))
        time.sleep(3)
        
        # 启动Beat
        print_colored("启动Beat...", Colors.BLUE)
        beat_cmd = [
            sys.executable, 'manage.py', 'celery_beat',
            '--settings=application.settings.zhi_celery',
            '--loglevel=info'
        ]
        beat_process = run_command(beat_cmd, background=True)
        processes.append(('Beat', beat_process))
        time.sleep(2)
        
        # 启动Flower
        print_colored("启动Flower...", Colors.BLUE)
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = 'application.settings.zhi_celery'
        
        flower_cmd = [
            'celery', '-A', 'zhi_celery.celery_app', 'flower',
            '--port=5555',
            '--loglevel=info'
        ]
        flower_process = subprocess.Popen(
            flower_cmd,
            cwd=backend_path,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        processes.append(('Flower', flower_process))
        time.sleep(2)
        
        print_colored("✅ 后台服务已启动:", Colors.GREEN)
        for name, process in processes:
            print_colored(f"   {name} PID: {process.pid}", Colors.BLUE)
        
        print_colored("🌐 Flower监控: http://localhost:5555", Colors.CYAN)
        print_colored("📁 日志目录: " + str(log_dir), Colors.BLUE)
        
        # 启动Web服务（前台）
        print_colored("🌐 启动Web管理服务...", Colors.GREEN)
        start_web()
        
    except Exception as e:
        print_colored(f"❌ 启动服务时出错: {e}", Colors.RED)
        # 清理已启动的进程
        for name, process in processes:
            try:
                process.terminate()
                print_colored(f"🛑 已停止 {name}", Colors.YELLOW)
            except:
                pass

def show_status():
    """显示服务状态"""
    print_colored("📊 ZhiCelery服务状态:", Colors.BLUE)
    
    # 这里可以添加更详细的状态检查逻辑
    # 目前只是示例
    print_colored("ℹ️  请使用系统任务管理器查看详细进程状态", Colors.YELLOW)
    print_colored("🌐 Web服务: http://localhost:8004", Colors.CYAN)
    print_colored("🌸 Flower监控: http://localhost:5555", Colors.CYAN)

def stop_all():
    """停止所有服务"""
    print_colored("🛑 停止所有ZhiCelery服务...", Colors.YELLOW)
    
    if os.name == 'nt':  # Windows
        # 停止相关进程
        subprocess.run(['taskkill', '/f', '/im', 'python.exe'], capture_output=True)
        subprocess.run(['taskkill', '/f', '/im', 'celery.exe'], capture_output=True)
    else:  # Unix/Linux
        subprocess.run(['pkill', '-f', 'celery'], capture_output=True)
        subprocess.run(['pkill', '-f', 'start_celery_service.py'], capture_output=True)
    
    print_colored("✅ 停止命令已发送", Colors.GREEN)

def show_help():
    """显示帮助信息"""
    help_text = """
🔧 ZhiCelery 使用说明:

📋 服务说明:
  • Worker: 处理异步任务的工作进程
  • Beat: 定时任务调度器，负责触发定时任务
  • Flower: Web界面的任务监控工具
  • Web服务: ZhiCelery管理后台

🌐 访问地址:
  • Web管理界面: http://localhost:8004
  • API文档: http://localhost:8004/api/celery/docs/
  • Flower监控: http://localhost:5555

📁 重要文件:
  • 配置文件: application/settings/zhi_celery.py
  • 任务文件: zhi_celery/tasks/
  • 日志目录: backend/logs/

💡 使用建议:
  • 开发环境: 选择"启动所有服务"
  • 生产环境: 分别启动各个服务
  • 监控任务: 使用Flower界面查看任务状态
    """
    print_colored(help_text, Colors.WHITE)

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print_colored("❌ 环境检查失败，请解决上述问题后重试", Colors.RED)
        sys.exit(1)
    
    print_colored("✅ 环境检查通过", Colors.GREEN)
    
    # 主循环
    while True:
        show_menu()
        choice = get_user_choice()
        
        if choice == '0':
            print_colored("👋 再见！", Colors.CYAN)
            break
        elif choice == '1':
            start_worker()
        elif choice == '2':
            start_beat()
        elif choice == '3':
            start_flower()
        elif choice == '4':
            start_web()
        elif choice == '5':
            start_all()
        elif choice == '6':
            show_status()
        elif choice == '7':
            stop_all()
        elif choice == '8':
            show_help()
        
        # 等待用户按键继续
        if choice != '0':
            input(f"\n{Colors.YELLOW}按回车键继续...{Colors.END}")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n👋 再见！", Colors.CYAN)
        sys.exit(0)
