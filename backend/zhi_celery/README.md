# ZhiCelery 异步任务管理子项目

## 📋 项目简介

ZhiCelery 是 ZhiAdmin 系统的异步任务管理子项目，基于 Django + Celery + RabbitMQ/Redis 架构，提供完整的异步任务处理、监控和调度功能。

### 🎯 核心功能

- **任务管理**: 任务执行、重试、撤销、状态监控
- **Worker监控**: 实时状态、性能统计、Worker控制
- **队列管理**: 队列监控、消息统计、队列控制
- **定时任务**: Cron/Interval调度、任务历史、状态管理
- **Web界面**: 管理后台、API文档、监控面板

## 🏗️ 架构设计

### 前后端分离架构
- **无 Services 层**: 简化架构，直接通过API提供服务
- **每个模型对应一个API文件**: 清晰的文件组织结构
- **自动CRUD API**: 基于 `@auto_crud_api` 装饰器自动生成标准接口
- **扩展功能**: 通过 `@api_route` 添加特定业务逻辑

### 核心模型
1. **CeleryTaskLog** - 任务日志记录
2. **CeleryWorkerStatus** - Worker状态监控
3. **CeleryQueueStats** - 队列统计信息
4. **CeleryScheduleTask** - 定时任务管理

## 📁 项目结构

```
backend/zhi_celery/
├── README.md                          # 项目文档
├── models.py                          # 数据模型
├── api.py                            # API入口配置
├── views.py                          # Web视图
├── celery_app.py                     # Celery应用配置
├── schedules.py                      # 调度配置
├── apis/                             # API接口层
│   ├── celery_task_log.py           # 任务日志API
│   ├── celery_worker_status.py      # Worker状态API
│   ├── celery_queue_stats.py        # 队列统计API
│   └── celery_schedule_task.py      # 定时任务API
└── tasks/                           # 异步任务
    ├── logger_tasks.py              # 日志相关任务
    └── oauth_tasks.py               # 认证相关任务
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Django 4.0+
- Celery 5.0+
- Redis/RabbitMQ
- PostgreSQL/MySQL

### 2. 安装依赖

```bash
pip install celery[redis]
pip install django-celery-beat
pip install django-celery-results
pip install flower
```

### 3. 配置设置

在 `application/settings/zhi_celery.py` 中配置：

```python
# Celery配置
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'django-db'
CELERY_CACHE_BACKEND = 'django-cache'

# 队列配置
CELERY_TASK_ROUTES = {
    'zhi_celery.tasks.*': {'queue': 'default'},
    'zhi_logger.tasks.*': {'queue': 'logger'},
}
```

### 4. 数据库迁移

```bash
python manage.py makemigrations zhi_celery
python manage.py migrate
```

## 🎮 启动服务

### 方式一：使用子项目启动脚本（推荐）

```bash
# 进入 zhi_celery 目录
cd backend/zhi_celery

# 启动 Worker
python start.py worker

# 启动 Beat 调度器
python start.py beat

# 启动 Flower 监控
python start.py flower

# 启动 Web 管理服务
python start.py web

# 自定义配置启动
python start.py worker --concurrency=4 --loglevel=debug
```

### 方式二：使用交互式启动脚本

```bash
# 进入 backend 目录
cd backend

# 运行交互式启动脚本
python zhi_celery/quick_start.py
```

### 方式三：使用全局启动脚本

```bash
# 启动所有服务（Worker + Beat + Flower）
python backend/zhi_scripts/start_celery.py --all

# 仅启动Worker
python backend/zhi_scripts/start_celery.py --worker

# 仅启动Beat调度器
python backend/zhi_scripts/start_celery.py --beat

# 仅启动Flower监控
python backend/zhi_scripts/start_celery.py --flower
```

### 方式四：手动启动各组件

```bash
# 设置环境变量
export DJANGO_SETTINGS_MODULE=application.settings.zhi_celery

# 启动Worker
celery -A zhi_celery.celery_app worker \
    --loglevel=info \
    --queues=default,logger,oauth,system \
    --concurrency=2 \
    --pool=solo

# 启动Beat调度器
celery -A zhi_celery.celery_app beat \
    --loglevel=info \
    --scheduler django_celery_beat.schedulers:DatabaseScheduler

# 启动Flower监控
celery -A zhi_celery.celery_app flower \
    --port=5555

# 启动Web服务
python manage.py runserver 0.0.0.0:8004 \
    --settings=application.settings.zhi_celery
```

## 🌐 服务访问

| 服务 | 地址 | 说明 |
|------|------|------|
| Web管理界面 | http://localhost:8004 | ZhiCelery管理后台 |
| API文档 | http://localhost:8004/api/celery/docs/ | Swagger API文档 |
| Flower监控 | http://localhost:5555 | Celery任务监控 |

## 📚 API接口

### 任务管理 API

```bash
# 获取任务列表
GET /api/celery/celery_task_logs/

# 获取活跃任务
GET /api/celery/celery_task_logs/active_tasks

# 重试任务
POST /api/celery/celery_task_logs/retry_task
{
    "task_id": "task-uuid"
}

# 撤销任务
POST /api/celery/celery_task_logs/revoke_task
{
    "task_id": "task-uuid",
    "terminate": true
}
```

### Worker管理 API

```bash
# 获取Worker列表
GET /api/celery/celery_workers/

# 获取实时Worker状态
GET /api/celery/celery_workers/live_status

# 获取Worker详细统计
GET /api/celery/celery_workers/worker_stats?worker_name=worker@hostname

# 关闭Worker
POST /api/celery/celery_workers/shutdown_worker
{
    "worker_name": "worker@hostname"
}
```

### 队列管理 API

```bash
# 获取队列列表
GET /api/celery/celery_queues/

# 获取队列信息
GET /api/celery/celery_queues/queue_info

# 获取队列长度
GET /api/celery/celery_queues/queue_length?queue_name=default

# 清空队列
POST /api/celery/celery_queues/purge_queue
{
    "queue_name": "default"
}
```

### 定时任务 API

```bash
# 获取定时任务列表
GET /api/celery/celery_schedules/

# 创建定时任务
POST /api/celery/celery_schedules/
{
    "name": "test_task",
    "task": "zhi_celery.tasks.test_task",
    "schedule_type": "crontab",
    "crontab_minute": "0",
    "crontab_hour": "*/2",
    "enabled": true
}

# 立即执行定时任务
POST /api/celery/celery_schedules/run_now
{
    "schedule_id": 1
}

# 启用/禁用定时任务
POST /api/celery/celery_schedules/toggle_schedule
{
    "schedule_id": 1
}

# 验证Cron表达式
GET /api/celery/celery_schedules/validate_cron?expression=0 */2 * * *
```

## 🔧 配置说明

### Celery配置

```python
# 基础配置
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'django-db'
CELERY_TIMEZONE = 'Asia/Shanghai'

# 任务配置
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30分钟
CELERY_TASK_SOFT_TIME_LIMIT = 25 * 60  # 25分钟

# Worker配置
CELERY_WORKER_CONCURRENCY = 2
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_DISABLE_RATE_LIMITS = True

# 队列配置
CELERY_TASK_ROUTES = {
    'zhi_celery.tasks.logger_tasks.*': {'queue': 'logger'},
    'zhi_celery.tasks.oauth_tasks.*': {'queue': 'oauth'},
    'zhi_celery.tasks.system_tasks.*': {'queue': 'system'},
}

# Beat配置
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
```

## 📊 监控和日志

### 日志配置

```python
LOGGING = {
    'loggers': {
        'celery': {
            'handlers': ['celery'],
            'level': 'INFO',
        },
        'celery.task': {
            'handlers': ['celery'],
            'level': 'INFO',
        },
    },
    'handlers': {
        'celery': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/celery.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
        },
    },
}
```

### 性能监控

- **Flower**: Web界面监控任务执行情况
- **日志记录**: 详细的任务执行日志
- **数据库统计**: 任务成功率、执行时间统计
- **Worker状态**: 实时监控Worker健康状态

## 🛠️ 开发指南

### 创建新任务

```python
# zhi_celery/tasks/my_tasks.py
from celery import shared_task
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)

@shared_task(bind=True, name='my_custom_task')
def my_custom_task(self, param1, param2):
    """自定义异步任务"""
    try:
        # 任务逻辑
        result = process_data(param1, param2)
        
        logger.info(f"任务执行成功: {result}")
        return result
        
    except Exception as exc:
        logger.error(f"任务执行失败: {exc}")
        raise self.retry(exc=exc, countdown=60, max_retries=3)
```

### 添加定时任务

```python
# 通过API创建
POST /api/celery/celery_schedules/
{
    "name": "daily_cleanup",
    "task": "zhi_celery.tasks.my_tasks.cleanup_task",
    "schedule_type": "crontab",
    "crontab_minute": "0",
    "crontab_hour": "2",
    "crontab_day_of_month": "*",
    "crontab_month_of_year": "*",
    "crontab_day_of_week": "*",
    "enabled": true,
    "description": "每日凌晨2点清理任务"
}
```

## 🚨 故障排除

### 常见问题

1. **Worker无法启动**
   - 检查Redis/RabbitMQ连接
   - 确认Django设置正确
   - 查看错误日志

2. **任务执行失败**
   - 检查任务代码逻辑
   - 确认依赖包安装
   - 查看Celery日志

3. **Beat调度不工作**
   - 确认数据库迁移完成
   - 检查定时任务配置
   - 验证时区设置

### 日志查看

```bash
# 查看Celery日志
tail -f logs/celery.log

# 查看Worker日志
celery -A zhi_celery.celery_app events

# 查看Beat日志
tail -f logs/celery_beat.log
```

## 📞 技术支持

- **项目地址**: https://github.com/your-org/zhi-admin
- **文档地址**: https://docs.zhi-admin.com
- **问题反馈**: https://github.com/your-org/zhi-admin/issues

## 🔄 任务示例

### 基础任务示例

```python
# zhi_celery/tasks/example_tasks.py
from celery import shared_task
from zhi_common.zhi_logger import get_logger
from django.core.mail import send_mail

logger = get_logger(__name__)

@shared_task(bind=True, name='send_email_task')
def send_email_task(self, subject, message, recipient_list):
    """发送邮件任务"""
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email='<EMAIL>',
            recipient_list=recipient_list,
            fail_silently=False,
        )
        logger.info(f"邮件发送成功: {recipient_list}")
        return f"邮件已发送给 {len(recipient_list)} 个收件人"

    except Exception as exc:
        logger.error(f"邮件发送失败: {exc}")
        raise self.retry(exc=exc, countdown=60, max_retries=3)

@shared_task(name='data_processing_task')
def data_processing_task(data_id):
    """数据处理任务"""
    logger.info(f"开始处理数据: {data_id}")

    # 模拟数据处理
    import time
    time.sleep(10)

    logger.info(f"数据处理完成: {data_id}")
    return f"数据 {data_id} 处理完成"
```

### 调用任务

```python
# 立即执行
from zhi_celery.tasks.example_tasks import send_email_task
result = send_email_task.delay(
    subject="测试邮件",
    message="这是一封测试邮件",
    recipient_list=["<EMAIL>"]
)

# 延迟执行（10秒后）
result = send_email_task.apply_async(
    args=["测试邮件", "延迟发送", ["<EMAIL>"]],
    countdown=10
)

# 定时执行
from datetime import datetime, timedelta
eta = datetime.now() + timedelta(hours=1)
result = send_email_task.apply_async(
    args=["定时邮件", "一小时后发送", ["<EMAIL>"]],
    eta=eta
)
```

## 🎯 最佳实践

### 1. 任务设计原则

- **幂等性**: 任务可以安全地重复执行
- **原子性**: 任务要么完全成功，要么完全失败
- **超时处理**: 设置合理的任务超时时间
- **错误处理**: 实现完善的异常处理和重试机制

### 2. 性能优化

```python
# 使用批量处理
@shared_task
def batch_process_users(user_ids):
    """批量处理用户数据"""
    from django.contrib.auth.models import User

    users = User.objects.filter(id__in=user_ids)
    for user in users:
        # 处理单个用户
        process_single_user(user)

# 避免在任务中进行大量数据库查询
@shared_task
def optimized_task(data):
    """优化的任务"""
    # 使用select_related和prefetch_related
    # 批量操作数据库
    # 使用缓存减少重复查询
    pass
```

### 3. 监控和告警

```python
# 任务状态回调
@shared_task(bind=True)
def monitored_task(self):
    """带监控的任务"""
    try:
        # 更新任务状态
        self.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100}
        )

        # 执行任务逻辑
        result = do_work()

        return {'status': 'SUCCESS', 'result': result}

    except Exception as exc:
        # 记录错误并发送告警
        logger.error(f"任务执行失败: {exc}")
        send_alert_notification(str(exc))
        raise
```

## 🔐 安全配置

### 1. 生产环境配置

```python
# application/settings/production.py
import os

# 使用环境变量
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND')

# 安全配置
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = False
CELERY_TASK_IGNORE_RESULT = False

# 限制任务执行时间
CELERY_TASK_TIME_LIMIT = 300  # 5分钟
CELERY_TASK_SOFT_TIME_LIMIT = 240  # 4分钟

# Worker安全配置
CELERY_WORKER_DISABLE_RATE_LIMITS = False
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_MAX_MEMORY_PER_CHILD = 200000  # 200MB
```

### 2. 权限控制

```python
# 任务权限装饰器
from functools import wraps
from django.contrib.auth.models import User

def require_permission(permission):
    def decorator(task_func):
        @wraps(task_func)
        def wrapper(*args, **kwargs):
            user_id = kwargs.get('user_id')
            if user_id:
                user = User.objects.get(id=user_id)
                if not user.has_perm(permission):
                    raise PermissionError(f"用户无权限执行任务: {permission}")
            return task_func(*args, **kwargs)
        return wrapper
    return decorator

@shared_task
@require_permission('zhi_celery.can_execute_admin_task')
def admin_task(user_id, **kwargs):
    """需要管理员权限的任务"""
    pass
```

## 📈 扩展功能

### 1. 自定义任务状态

```python
from celery import states

# 自定义状态
CUSTOM_STATES = {
    'PROCESSING': 'PROCESSING',
    'VALIDATING': 'VALIDATING',
    'COMPLETED': 'COMPLETED'
}

@shared_task(bind=True)
def complex_task(self):
    """复杂任务状态管理"""
    self.update_state(state='PROCESSING', meta={'step': 1})
    # 处理步骤1

    self.update_state(state='VALIDATING', meta={'step': 2})
    # 验证步骤

    self.update_state(state='COMPLETED', meta={'step': 3})
    return {'status': 'success'}
```

### 2. 任务链和组

```python
from celery import chain, group, chord

# 任务链 - 顺序执行
task_chain = chain(
    task1.s(arg1),
    task2.s(),
    task3.s()
)
result = task_chain.apply_async()

# 任务组 - 并行执行
task_group = group(
    task1.s(1),
    task1.s(2),
    task1.s(3)
)
result = task_group.apply_async()

# 任务和弦 - 并行执行后汇总
task_chord = chord(
    [task1.s(i) for i in range(10)],
    callback_task.s()
)
result = task_chord.apply_async()
```

## 🧪 测试

### 单元测试

```python
# tests/test_tasks.py
from django.test import TestCase
from celery import current_app
from zhi_celery.tasks.example_tasks import send_email_task

class TaskTestCase(TestCase):
    def setUp(self):
        # 使用同步模式测试
        current_app.conf.task_always_eager = True
        current_app.conf.task_eager_propagates = True

    def test_send_email_task(self):
        """测试邮件发送任务"""
        result = send_email_task.delay(
            subject="测试",
            message="测试消息",
            recipient_list=["<EMAIL>"]
        )

        self.assertTrue(result.successful())
        self.assertIn("邮件已发送", result.result)

    def tearDown(self):
        current_app.conf.task_always_eager = False
        current_app.conf.task_eager_propagates = False
```

### 集成测试

```bash
# 运行测试
python manage.py test zhi_celery.tests

# 测试特定任务
python manage.py test zhi_celery.tests.test_tasks.TaskTestCase.test_send_email_task
```

---

**ZhiCelery** - 让异步任务管理更简单！ 🚀
