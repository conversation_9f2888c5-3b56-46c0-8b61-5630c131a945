"""
ZhiCelery 服务层
提供Celery任务管理、监控和调度的核心业务逻辑
参考 zhi_oauth 的服务设计模式
"""

from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.db import transaction
from django.core.cache import cache
from celery import current_app
from celery.result import AsyncResult
from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule

try:
    from django_celery_results.models import TaskResult
except ImportError:
    TaskResult = None

from .celery_app import app as celery_app
from .models import CeleryTaskLog, CeleryWorkerStatus, CeleryQueueStats
from zhi_common.zhi_logger import get_logger

logger = get_logger(module_name="celery_service")


class CeleryManagementService:
    """Celery管理服务 - 核心业务逻辑"""
    
    def __init__(self):
        self.celery_app = celery_app
        self.cache_timeout = getattr(settings, 'CELERY_CACHE_TIMEOUT', 300)  # 5分钟缓存
        self.max_task_history = getattr(settings, 'CELERY_MAX_TASK_HISTORY', 1000)
    
    # ==================== 任务管理 ====================
    
    def get_task_list(self, status_filter: str = None, limit: int = 100) -> Dict[str, Any]:
        """
        获取任务列表
        
        Args:
            status_filter: 状态过滤器 (ACTIVE, SCHEDULED, SUCCESS, FAILURE, etc.)
            limit: 返回数量限制
            
        Returns:
            dict: 任务列表和统计信息
        """
        try:
            inspect = self.celery_app.control.inspect()
            
            # 获取活跃任务
            active_tasks = inspect.active() or {}
            
            # 获取预定任务
            scheduled_tasks = inspect.scheduled() or {}
            
            # 获取保留任务
            reserved_tasks = inspect.reserved() or {}
            
            # 获取历史任务（从结果存储）
            history_tasks = self._get_task_history(limit=limit)
            
            tasks = []
            
            # 处理活跃任务
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['id'],
                        'name': task['name'],
                        'args': task['args'],
                        'kwargs': task['kwargs'],
                        'worker': worker,
                        'state': 'ACTIVE',
                        'timestamp': task.get('time_start'),
                        'queue': self._get_task_queue(task['name']),
                    })
            
            # 处理预定任务
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['request']['id'],
                        'name': task['request']['task'],
                        'args': task['request']['args'],
                        'kwargs': task['request']['kwargs'],
                        'worker': worker,
                        'state': 'SCHEDULED',
                        'eta': task['eta'],
                        'queue': self._get_task_queue(task['request']['task']),
                    })
            
            # 添加历史任务
            tasks.extend(history_tasks)
            
            # 应用状态过滤
            if status_filter:
                tasks = [t for t in tasks if t['state'] == status_filter]
            
            # 限制返回数量
            tasks = tasks[:limit]
            
            # 统计信息
            stats = {
                'total': len(tasks),
                'active_count': sum(len(tasks) for tasks in active_tasks.values()),
                'scheduled_count': sum(len(tasks) for tasks in scheduled_tasks.values()),
                'reserved_count': sum(len(tasks) for tasks in reserved_tasks.values()),
                'success_count': len([t for t in history_tasks if t['state'] == 'SUCCESS']),
                'failure_count': len([t for t in history_tasks if t['state'] == 'FAILURE']),
            }
            
            return {
                'success': True,
                'data': {
                    'tasks': tasks,
                    'stats': stats,
                }
            }
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '获取任务列表失败'
            }
    
    def get_task_detail(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 任务详细信息
        """
        try:
            result = AsyncResult(task_id, app=self.celery_app)
            
            task_info = {
                'id': task_id,
                'state': result.state,
                'result': result.result,
                'traceback': result.traceback,
                'info': result.info,
                'successful': result.successful(),
                'failed': result.failed(),
                'ready': result.ready(),
                'date_done': result.date_done.isoformat() if result.date_done else None,
            }
            
            # 如果任务还在运行，获取更多信息
            if not result.ready():
                inspect = self.celery_app.control.inspect()
                active_tasks = inspect.active() or {}
                
                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            task_info.update({
                                'worker': worker,
                                'name': task['name'],
                                'args': task['args'],
                                'kwargs': task['kwargs'],
                                'time_start': task.get('time_start'),
                                'queue': self._get_task_queue(task['name']),
                            })
                            break
            
            # 尝试从数据库获取更多信息
            if TaskResult:
                try:
                    task_result = TaskResult.objects.get(task_id=task_id)
                    task_info.update({
                        'task_name': task_result.task_name,
                        'task_args': task_result.task_args,
                        'task_kwargs': task_result.task_kwargs,
                        'date_created': task_result.date_created.isoformat(),
                        'date_done': task_result.date_done.isoformat() if task_result.date_done else None,
                        'meta': task_result.meta,
                    })
                except TaskResult.DoesNotExist:
                    pass
            
            return {
                'success': True,
                'data': task_info
            }
            
        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '获取任务详情失败'
            }
    
    def retry_task(self, task_id: str) -> Dict[str, Any]:
        """
        重试任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 重试结果
        """
        try:
            result = AsyncResult(task_id, app=self.celery_app)
            
            if result.failed():
                # 重新执行任务
                new_result = result.retry()
                
                logger.info(f"任务重试成功: {task_id} -> {new_result.id}")
                
                return {
                    'success': True,
                    'message': '任务重试成功',
                    'data': {
                        'original_task_id': task_id,
                        'new_task_id': new_result.id
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'task_not_failed',
                    'message': '只能重试失败的任务'
                }
                
        except Exception as e:
            logger.error(f"任务重试失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '任务重试失败'
            }
    
    def revoke_task(self, task_id: str, terminate: bool = True) -> Dict[str, Any]:
        """
        撤销任务
        
        Args:
            task_id: 任务ID
            terminate: 是否终止正在运行的任务
            
        Returns:
            dict: 撤销结果
        """
        try:
            self.celery_app.control.revoke(task_id, terminate=terminate)
            
            logger.info(f"任务撤销成功: {task_id}, terminate={terminate}")
            
            return {
                'success': True,
                'message': '任务撤销成功',
                'data': {
                    'task_id': task_id,
                    'terminated': terminate
                }
            }
            
        except Exception as e:
            logger.error(f"任务撤销失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '任务撤销失败'
            }
    
    # ==================== 私有方法 ====================
    
    def _get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务历史记录"""
        history_tasks = []
        
        if TaskResult:
            try:
                recent_results = TaskResult.objects.order_by('-date_created')[:limit]
                
                for task_result in recent_results:
                    history_tasks.append({
                        'id': task_result.task_id,
                        'name': task_result.task_name,
                        'args': task_result.task_args,
                        'kwargs': task_result.task_kwargs,
                        'state': task_result.status,
                        'result': task_result.result,
                        'date_created': task_result.date_created.isoformat(),
                        'date_done': task_result.date_done.isoformat() if task_result.date_done else None,
                        'worker': None,
                        'queue': self._get_task_queue(task_result.task_name),
                    })
            except Exception as e:
                logger.error(f"获取任务历史失败: {e}")
        
        return history_tasks
    
    def _get_task_queue(self, task_name: str) -> str:
        """根据任务名称获取队列名称"""
        task_routes = getattr(settings, 'CELERY_TASK_ROUTES', {})
        
        for pattern, route_config in task_routes.items():
            if pattern in task_name or task_name.startswith(pattern.replace('.*', '')):
                return route_config.get('queue', 'default')
        
        return 'default'


# 全局服务实例
celery_management_service = CeleryManagementService()
