"""
ZhiCelery 模型定义
用于存储Celery任务管理相关的数据
"""

from django.db import models
from zhi_common.zhi_model.core_model import ZhiCoreModel


class CeleryTaskLog(ZhiCoreModel):
    """Celery任务日志"""

    task_id = models.CharField(max_length=255, unique=True, verbose_name="任务ID")
    task_name = models.CharField(max_length=255, verbose_name="任务名称")
    task_args = models.JSONField(default=list, verbose_name="任务参数")
    task_kwargs = models.JSONField(default=dict, verbose_name="任务关键字参数")

    status = models.CharField(
        max_length=50,
        choices=[
            ('PENDING', '等待中'),
            ('STARTED', '已开始'),
            ('SUCCESS', '成功'),
            ('FAILURE', '失败'),
            ('RETRY', '重试中'),
            ('REVOKED', '已撤销'),
        ],
        default='PENDING',
        verbose_name="任务状态"
    )

    result = models.JSONField(null=True, blank=True, verbose_name="任务结果")
    traceback = models.TextField(null=True, blank=True, verbose_name="错误堆栈")

    worker = models.CharField(max_length=255, null=True, blank=True, verbose_name="执行Worker")
    queue = models.CharField(max_length=100, default='default', verbose_name="队列名称")

    started_at = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")

    runtime = models.FloatField(null=True, blank=True, verbose_name="运行时长(秒)")

    class Meta:
        db_table = 'celery_task_log'
        verbose_name = 'Celery任务日志'
        verbose_name_plural = 'Celery任务日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['task_id']),
            models.Index(fields=['status']),
            models.Index(fields=['queue']),
            models.Index(fields=['created_at']),
        ]


class CeleryWorkerStatus(ZhiCoreModel):
    """Celery Worker状态"""

    worker_name = models.CharField(max_length=255, unique=True, verbose_name="Worker名称")
    hostname = models.CharField(max_length=255, verbose_name="主机名")

    status = models.CharField(
        max_length=20,
        choices=[
            ('online', '在线'),
            ('offline', '离线'),
            ('busy', '忙碌'),
        ],
        default='offline',
        verbose_name="状态"
    )

    active_tasks = models.IntegerField(default=0, verbose_name="活跃任务数")
    processed_tasks = models.IntegerField(default=0, verbose_name="已处理任务数")

    pool_type = models.CharField(max_length=50, null=True, blank=True, verbose_name="进程池类型")
    concurrency = models.IntegerField(default=1, verbose_name="并发数")

    load_avg = models.FloatField(default=0.0, verbose_name="负载平均值")
    memory_usage = models.FloatField(default=0.0, verbose_name="内存使用率")

    last_heartbeat = models.DateTimeField(null=True, blank=True, verbose_name="最后心跳时间")

    class Meta:
        db_table = 'celery_worker_status'
        verbose_name = 'Celery Worker状态'
        verbose_name_plural = 'Celery Worker状态'
        ordering = ['-updated_at']


class CeleryQueueStats(ZhiCoreModel):
    """Celery队列统计"""

    queue_name = models.CharField(max_length=100, unique=True, verbose_name="队列名称")

    message_count = models.IntegerField(default=0, verbose_name="消息数量")
    consumer_count = models.IntegerField(default=0, verbose_name="消费者数量")

    message_rate = models.FloatField(default=0.0, verbose_name="消息速率")
    consumer_rate = models.FloatField(default=0.0, verbose_name="消费速率")

    total_processed = models.BigIntegerField(default=0, verbose_name="总处理数")
    total_failed = models.BigIntegerField(default=0, verbose_name="总失败数")

    class Meta:
        db_table = 'celery_queue_stats'
        verbose_name = 'Celery队列统计'
        verbose_name_plural = 'Celery队列统计'
        ordering = ['queue_name']


class CeleryScheduleTask(ZhiCoreModel):
    """自定义定时任务（扩展django-celery-beat）"""

    name = models.CharField(max_length=200, unique=True, verbose_name="任务名称")
    task = models.CharField(max_length=200, verbose_name="任务路径")

    # 任务参数
    args = models.JSONField(default=list, verbose_name="位置参数")
    kwargs = models.JSONField(default=dict, verbose_name="关键字参数")

    # 调度配置
    schedule_type = models.CharField(
        max_length=20,
        choices=[
            ('crontab', 'Crontab'),
            ('interval', 'Interval'),
        ],
        verbose_name="调度类型"
    )

    # Crontab配置
    crontab_minute = models.CharField(max_length=240, default='*', verbose_name="分钟")
    crontab_hour = models.CharField(max_length=96, default='*', verbose_name="小时")
    crontab_day_of_week = models.CharField(max_length=64, default='*', verbose_name="星期")
    crontab_day_of_month = models.CharField(max_length=124, default='*', verbose_name="日期")
    crontab_month_of_year = models.CharField(max_length=64, default='*', verbose_name="月份")

    # Interval配置
    interval_every = models.IntegerField(null=True, blank=True, verbose_name="间隔数")
    interval_period = models.CharField(
        max_length=24,
        choices=[
            ('days', '天'),
            ('hours', '小时'),
            ('minutes', '分钟'),
            ('seconds', '秒'),
        ],
        null=True, blank=True,
        verbose_name="间隔单位"
    )

    # 其他配置
    queue = models.CharField(max_length=100, default='default', verbose_name="队列")
    enabled = models.BooleanField(default=True, verbose_name="是否启用")

    # 统计信息
    total_run_count = models.PositiveIntegerField(default=0, verbose_name="总运行次数")
    last_run_at = models.DateTimeField(null=True, blank=True, verbose_name="最后运行时间")

    description = models.TextField(blank=True, verbose_name="描述")

    class Meta:
        db_table = 'celery_schedule_task'
        verbose_name = '定时任务'
        verbose_name_plural = '定时任务'
        ordering = ['-created_at']
