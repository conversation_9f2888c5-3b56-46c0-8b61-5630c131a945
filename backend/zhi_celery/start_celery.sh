#!/bin/bash
# ZhiCelery 异步任务管理子项目启动脚本
# 使用方式: ./start_celery.sh [worker|beat|flower|web|all]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_PATH="$PROJECT_ROOT/backend"

# 默认配置
SETTINGS_MODULE="application.settings.zhi_celery"
LOG_LEVEL="info"
CONCURRENCY=2
QUEUES="default,logger,oauth,system"
FLOWER_PORT=5555
WEB_PORT=8004

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查系统依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        print_message $RED "❌ Python未安装"
        exit 1
    fi
    
    # 检查Redis连接
    if ! python -c "import redis; redis.Redis().ping()" 2>/dev/null; then
        print_message $YELLOW "⚠️  Redis连接失败，请确保Redis服务正在运行"
    else
        print_message $GREEN "✅ Redis连接正常"
    fi
    
    # 检查必要的Python包
    local packages=("celery" "django" "redis")
    for package in "${packages[@]}"; do
        if python -c "import $package" 2>/dev/null; then
            print_message $GREEN "✅ $package"
        else
            print_message $RED "❌ $package 未安装"
            exit 1
        fi
    done
}

# 函数：启动Worker
start_worker() {
    print_message $GREEN "🔄 启动Celery Worker..."
    cd "$BACKEND_PATH"
    
    python manage.py celery_worker \
        --settings="$SETTINGS_MODULE" \
        --pool=solo \
        --loglevel="$LOG_LEVEL" \
        --queues="$QUEUES" \
        --concurrency="$CONCURRENCY"
}

# 函数：启动Beat
start_beat() {
    print_message $GREEN "⏰ 启动Celery Beat..."
    cd "$BACKEND_PATH"
    
    python manage.py celery_beat \
        --settings="$SETTINGS_MODULE" \
        --loglevel="$LOG_LEVEL"
}

# 函数：启动Flower
start_flower() {
    print_message $GREEN "🌸 启动Flower监控..."
    cd "$BACKEND_PATH"
    
    export DJANGO_SETTINGS_MODULE="$SETTINGS_MODULE"
    celery -A zhi_celery.celery_app flower \
        --port="$FLOWER_PORT" \
        --loglevel="$LOG_LEVEL"
}

# 函数：启动Web服务
start_web() {
    print_message $GREEN "🌐 启动ZhiCelery Web管理服务..."
    cd "$BACKEND_PATH"
    
    python ../zhi_scripts/start_celery_service.py \
        --port="$WEB_PORT" \
        --settings="$SETTINGS_MODULE"
}

# 函数：启动所有服务
start_all() {
    print_message $GREEN "🚀 启动所有ZhiCelery服务..."
    
    # 创建日志目录
    mkdir -p "$BACKEND_PATH/logs"
    
    # 启动Worker（后台）
    print_message $BLUE "启动Worker..."
    nohup bash -c "cd '$BACKEND_PATH' && python manage.py celery_worker --settings='$SETTINGS_MODULE' --pool=solo --loglevel='$LOG_LEVEL' --queues='$QUEUES' --concurrency='$CONCURRENCY'" > "$BACKEND_PATH/logs/celery_worker.log" 2>&1 &
    WORKER_PID=$!
    
    sleep 3
    
    # 启动Beat（后台）
    print_message $BLUE "启动Beat..."
    nohup bash -c "cd '$BACKEND_PATH' && python manage.py celery_beat --settings='$SETTINGS_MODULE' --loglevel='$LOG_LEVEL'" > "$BACKEND_PATH/logs/celery_beat.log" 2>&1 &
    BEAT_PID=$!
    
    sleep 2
    
    # 启动Flower（后台）
    print_message $BLUE "启动Flower..."
    nohup bash -c "cd '$BACKEND_PATH' && export DJANGO_SETTINGS_MODULE='$SETTINGS_MODULE' && celery -A zhi_celery.celery_app flower --port='$FLOWER_PORT' --loglevel='$LOG_LEVEL'" > "$BACKEND_PATH/logs/celery_flower.log" 2>&1 &
    FLOWER_PID=$!
    
    sleep 2
    
    # 启动Web服务（前台）
    print_message $GREEN "✅ 后台服务已启动:"
    print_message $BLUE "   Worker PID: $WORKER_PID"
    print_message $BLUE "   Beat PID: $BEAT_PID"
    print_message $BLUE "   Flower PID: $FLOWER_PID"
    print_message $BLUE "   Flower监控: http://localhost:$FLOWER_PORT"
    print_message $BLUE "   日志目录: $BACKEND_PATH/logs/"
    
    print_message $GREEN "🌐 启动Web管理服务..."
    start_web
}

# 函数：停止所有服务
stop_all() {
    print_message $YELLOW "🛑 停止所有ZhiCelery服务..."
    
    # 停止相关进程
    pkill -f "celery.*worker" || true
    pkill -f "celery.*beat" || true
    pkill -f "celery.*flower" || true
    pkill -f "start_celery_service.py" || true
    
    print_message $GREEN "✅ 所有服务已停止"
}

# 函数：显示服务状态
show_status() {
    print_message $BLUE "📊 ZhiCelery服务状态:"
    
    if pgrep -f "celery.*worker" > /dev/null; then
        print_message $GREEN "✅ Worker: 运行中"
    else
        print_message $RED "❌ Worker: 未运行"
    fi
    
    if pgrep -f "celery.*beat" > /dev/null; then
        print_message $GREEN "✅ Beat: 运行中"
    else
        print_message $RED "❌ Beat: 未运行"
    fi
    
    if pgrep -f "celery.*flower" > /dev/null; then
        print_message $GREEN "✅ Flower: 运行中 (http://localhost:$FLOWER_PORT)"
    else
        print_message $RED "❌ Flower: 未运行"
    fi
    
    if pgrep -f "start_celery_service.py" > /dev/null; then
        print_message $GREEN "✅ Web服务: 运行中 (http://localhost:$WEB_PORT)"
    else
        print_message $RED "❌ Web服务: 未运行"
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "ZhiCelery 异步任务管理子项目启动脚本"
    echo ""
    echo "使用方式:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  worker    启动Celery Worker"
    echo "  beat      启动Celery Beat调度器"
    echo "  flower    启动Flower监控"
    echo "  web       启动Web管理服务"
    echo "  all       启动所有服务"
    echo "  stop      停止所有服务"
    echo "  status    显示服务状态"
    echo "  help      显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SETTINGS_MODULE   Django设置模块 (默认: $SETTINGS_MODULE)"
    echo "  LOG_LEVEL         日志级别 (默认: $LOG_LEVEL)"
    echo "  CONCURRENCY       Worker并发数 (默认: $CONCURRENCY)"
    echo "  QUEUES            队列名称 (默认: $QUEUES)"
    echo "  FLOWER_PORT       Flower端口 (默认: $FLOWER_PORT)"
    echo "  WEB_PORT          Web服务端口 (默认: $WEB_PORT)"
    echo ""
    echo "示例:"
    echo "  $0 worker                    # 启动Worker"
    echo "  $0 all                       # 启动所有服务"
    echo "  CONCURRENCY=4 $0 worker      # 启动4个并发的Worker"
    echo "  LOG_LEVEL=debug $0 beat      # 以debug级别启动Beat"
}

# 主函数
main() {
    # 读取环境变量覆盖默认配置
    SETTINGS_MODULE="${SETTINGS_MODULE:-$SETTINGS_MODULE}"
    LOG_LEVEL="${LOG_LEVEL:-$LOG_LEVEL}"
    CONCURRENCY="${CONCURRENCY:-$CONCURRENCY}"
    QUEUES="${QUEUES:-$QUEUES}"
    FLOWER_PORT="${FLOWER_PORT:-$FLOWER_PORT}"
    WEB_PORT="${WEB_PORT:-$WEB_PORT}"
    
    # 显示启动信息
    print_message $BLUE "=" "$(printf '=%.0s' {1..50})"
    print_message $BLUE "🚀 ZhiCelery 异步任务管理子项目"
    print_message $BLUE "=" "$(printf '=%.0s' {1..50})"
    print_message $BLUE "📁 项目路径: $PROJECT_ROOT"
    print_message $BLUE "⚙️  设置模块: $SETTINGS_MODULE"
    print_message $BLUE "📊 日志级别: $LOG_LEVEL"
    
    # 检查依赖
    check_dependencies
    
    # 根据参数执行相应操作
    case "${1:-help}" in
        worker)
            start_worker
            ;;
        beat)
            start_beat
            ;;
        flower)
            start_flower
            ;;
        web)
            start_web
            ;;
        all)
            start_all
            ;;
        stop)
            stop_all
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C信号
trap 'print_message $YELLOW "\n🛑 收到停止信号，正在关闭服务..."; stop_all; exit 0' INT TERM

# 执行主函数
main "$@"
