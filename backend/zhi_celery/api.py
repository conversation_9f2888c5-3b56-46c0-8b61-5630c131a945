"""
ZhiCelery API 配置
参考 simple_example_product 的简化API设计
"""

from ninja_extra import NinjaExtraAPI
from .apis.celery_apis import (
    CeleryTaskLogControllerAPI,
    CeleryWorkerStatusControllerAPI,
    CeleryQueueStatsControllerAPI,
    CeleryScheduleTaskControllerAPI
)
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers

# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiCelery API",
    version="1.0.0",
    description="ZhiAdmin Celery 异步任务管理系统 API",
    auth=GlobalOAuth2,
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="docs",
    openapi_url="openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器 - 使用自动CRUD API
api.register_controllers(CeleryTaskLogControllerAPI)      # 任务日志管理
api.register_controllers(CeleryWorkerStatusControllerAPI) # Worker状态管理
api.register_controllers(CeleryQueueStatsControllerAPI)   # 队列统计管理
api.register_controllers(CeleryScheduleTaskControllerAPI) # 定时任务管理
