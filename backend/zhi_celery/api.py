"""
ZhiCelery API 配置
参考 zhi_oauth 的 API 架构设计
"""

from ninja_extra import NinjaExtraAPI
from .apis.celery_management import CeleryManagementControllerAPI
from .apis.celery_monitoring import CeleryMonitoringControllerAPI
from .apis.celery_scheduling import CelerySchedulingControllerAPI
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers

# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiCelery API",
    version="1.0.0",
    description="ZhiAdmin Celery 异步任务管理系统 API",
    auth=GlobalOAuth2,
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="docs",
    openapi_url="openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器
api.register_controllers(CeleryManagementControllerAPI)  # 任务管理控制器
api.register_controllers(CeleryMonitoringControllerAPI)  # 监控控制器
api.register_controllers(CelerySchedulingControllerAPI)  # 调度控制器
