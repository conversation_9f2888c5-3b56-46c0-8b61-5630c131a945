"""
ZhiCelery API 配置
每个模型对应一个独立的API文件
"""

from ninja_extra import NinjaExtraAPI
from .apis.celery_task_log import CeleryTaskLogControllerAPI
from .apis.celery_worker_status import CeleryWorkerStatusControllerAPI
from .apis.celery_queue_stats import CeleryQueueStatsControllerAPI
from .apis.celery_schedule_task import CeleryScheduleTaskControllerAPI
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers

# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiCelery API",
    version="1.0.0",
    description="ZhiAdmin Celery 异步任务管理系统 API",
    auth=GlobalOAuth2,
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="docs",
    openapi_url="openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器 - 每个模型对应一个API文件
api.register_controllers(CeleryTaskLogControllerAPI)      # 任务日志管理 API
api.register_controllers(CeleryWorkerStatusControllerAPI) # Worker状态管理 API
api.register_controllers(CeleryQueueStatsControllerAPI)   # 队列统计管理 API
api.register_controllers(CeleryScheduleTaskControllerAPI) # 定时任务管理 API
