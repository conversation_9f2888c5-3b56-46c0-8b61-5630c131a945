#!/usr/bin/env python3
"""
ZhiCelery 子项目简化启动脚本
直接在 zhi_celery 目录下运行

使用方式:
cd backend/zhi_celery
python start.py [worker|beat|flower|web|all]
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 项目路径设置
current_dir = Path(__file__).parent
backend_dir = current_dir.parent
project_root = backend_dir.parent

# 添加 backend 目录到 Python 路径
sys.path.insert(0, str(backend_dir))

# 设置工作目录为 backend
os.chdir(backend_dir)

# 默认配置
SETTINGS_MODULE = 'application.settings.zhi_celery'
LOG_LEVEL = 'info'
CONCURRENCY = 2
QUEUES = 'default,logger,oauth,system'
FLOWER_PORT = 5555
WEB_PORT = 8004

def print_info(message, color_code=94):
    """打印带颜色的信息"""
    print(f"\033[{color_code}m{message}\033[0m")

def print_success(message):
    """打印成功信息"""
    print_info(f"✅ {message}", 92)

def print_error(message):
    """打印错误信息"""
    print_info(f"❌ {message}", 91)

def print_warning(message):
    """打印警告信息"""
    print_info(f"⚠️  {message}", 93)

def check_environment():
    """检查环境"""
    print_info("🔍 检查环境...")
    
    # 检查 Django
    try:
        import django
        print_success(f"Django {django.get_version()}")
    except ImportError:
        print_error("Django 未安装")
        return False
    
    # 检查 Celery
    try:
        import celery
        print_success(f"Celery {celery.__version__}")
    except ImportError:
        print_error("Celery 未安装")
        return False
    
    # 检查 Redis
    try:
        import redis
        r = redis.Redis()
        r.ping()
        print_success("Redis 连接正常")
    except Exception as e:
        print_warning(f"Redis 连接失败: {e}")
    
    return True

def run_command(cmd, description):
    """运行命令"""
    print_info(f"🚀 {description}")
    print_info(f"📋 命令: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print_warning("用户中断操作")
    except subprocess.CalledProcessError as e:
        print_error(f"命令执行失败: {e}")
    except FileNotFoundError:
        print_error(f"命令未找到: {cmd[0]}")

def start_worker():
    """启动 Worker"""
    # 尝试使用标准 celery 命令
    cmd = [
        'celery', '-A', 'zhi_celery.celery_app', 'worker',
        '--loglevel', LOG_LEVEL,
        '--queues', QUEUES,
        '--concurrency', str(CONCURRENCY),
        '--pool', 'solo'  # Windows 兼容
    ]
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = SETTINGS_MODULE
    
    print_info(f"🔄 启动 Celery Worker")
    print_info(f"📋 命令: {' '.join(cmd)}")
    print_info(f"⚙️  设置: {SETTINGS_MODULE}")
    
    try:
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print_warning("Worker 已停止")
    except subprocess.CalledProcessError as e:
        print_error(f"Worker 启动失败: {e}")
    except FileNotFoundError:
        print_error("未找到 celery 命令，请确保已安装 celery")

def start_beat():
    """启动 Beat"""
    cmd = [
        'celery', '-A', 'zhi_celery.celery_app', 'beat',
        '--loglevel', LOG_LEVEL,
        '--scheduler', 'django_celery_beat.schedulers:DatabaseScheduler'
    ]
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = SETTINGS_MODULE
    
    print_info(f"⏰ 启动 Celery Beat")
    print_info(f"📋 命令: {' '.join(cmd)}")
    print_info(f"⚙️  设置: {SETTINGS_MODULE}")
    
    try:
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print_warning("Beat 已停止")
    except subprocess.CalledProcessError as e:
        print_error(f"Beat 启动失败: {e}")
    except FileNotFoundError:
        print_error("未找到 celery 命令，请确保已安装 celery")

def start_flower():
    """启动 Flower"""
    cmd = [
        'celery', '-A', 'zhi_celery.celery_app', 'flower',
        '--port', str(FLOWER_PORT),
        '--loglevel', LOG_LEVEL
    ]
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = SETTINGS_MODULE
    
    print_info(f"🌸 启动 Flower 监控")
    print_info(f"📋 命令: {' '.join(cmd)}")
    print_info(f"🌐 访问地址: http://localhost:{FLOWER_PORT}")
    
    try:
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print_warning("Flower 已停止")
    except subprocess.CalledProcessError as e:
        print_error(f"Flower 启动失败: {e}")
    except FileNotFoundError:
        print_error("未找到 celery 命令，请确保已安装 celery")

def start_web():
    """启动 Web 服务"""
    cmd = [
        sys.executable, 'manage.py', 'runserver',
        f'0.0.0.0:{WEB_PORT}',
        '--settings', SETTINGS_MODULE
    ]
    
    print_info(f"🌐 启动 ZhiCelery Web 管理服务")
    print_info(f"📋 命令: {' '.join(cmd)}")
    print_info(f"🌐 访问地址:")
    print_info(f"   • 管理界面: http://localhost:{WEB_PORT}")
    print_info(f"   • API文档: http://localhost:{WEB_PORT}/api/celery/docs/")
    print_info(f"🛑 按 Ctrl+C 停止服务")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print_warning("Web 服务已停止")
    except subprocess.CalledProcessError as e:
        print_error(f"Web 服务启动失败: {e}")

def start_all():
    """启动所有服务"""
    print_error("暂不支持同时启动所有服务")
    print_info("请分别在不同终端中启动各个服务:")
    print_info("  python start.py worker")
    print_info("  python start.py beat")
    print_info("  python start.py flower")
    print_info("  python start.py web")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='ZhiCelery 子项目启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start.py worker     # 启动 Worker
  python start.py beat       # 启动 Beat
  python start.py flower     # 启动 Flower
  python start.py web        # 启动 Web 服务
        """
    )
    
    parser.add_argument(
        'service',
        choices=['worker', 'beat', 'flower', 'web', 'all'],
        help='要启动的服务'
    )
    
    parser.add_argument(
        '--settings',
        default=SETTINGS_MODULE,
        help=f'Django 设置模块 (默认: {SETTINGS_MODULE})'
    )
    
    parser.add_argument(
        '--loglevel',
        default=LOG_LEVEL,
        help=f'日志级别 (默认: {LOG_LEVEL})'
    )
    
    parser.add_argument(
        '--concurrency',
        type=int,
        default=CONCURRENCY,
        help=f'Worker 并发数 (默认: {CONCURRENCY})'
    )
    
    parser.add_argument(
        '--queues',
        default=QUEUES,
        help=f'队列名称 (默认: {QUEUES})'
    )
    
    parser.add_argument(
        '--flower-port',
        type=int,
        default=FLOWER_PORT,
        help=f'Flower 端口 (默认: {FLOWER_PORT})'
    )
    
    parser.add_argument(
        '--web-port',
        type=int,
        default=WEB_PORT,
        help=f'Web 服务端口 (默认: {WEB_PORT})'
    )
    
    args = parser.parse_args()

    # 更新配置
    settings_module = args.settings
    log_level = args.loglevel
    concurrency = args.concurrency
    queues = args.queues
    flower_port = args.flower_port
    web_port = args.web_port

    # 更新全局变量
    global SETTINGS_MODULE, LOG_LEVEL, CONCURRENCY, QUEUES, FLOWER_PORT, WEB_PORT
    SETTINGS_MODULE = settings_module
    LOG_LEVEL = log_level
    CONCURRENCY = concurrency
    QUEUES = queues
    FLOWER_PORT = flower_port
    WEB_PORT = web_port
    
    # 显示启动信息
    print_info("=" * 60)
    print_info("🚀 ZhiCelery 异步任务管理子项目")
    print_info("=" * 60)
    print_info(f"📁 工作目录: {os.getcwd()}")
    print_info(f"⚙️  设置模块: {SETTINGS_MODULE}")
    print_info(f"📊 日志级别: {LOG_LEVEL}")
    
    # 检查环境
    if not check_environment():
        print_error("环境检查失败")
        sys.exit(1)
    
    # 启动对应服务
    service_map = {
        'worker': start_worker,
        'beat': start_beat,
        'flower': start_flower,
        'web': start_web,
        'all': start_all
    }
    
    service_func = service_map[args.service]
    service_func()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print_warning("\n👋 再见！")
        sys.exit(0)
