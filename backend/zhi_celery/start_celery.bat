@echo off
REM ZhiCelery 异步任务管理子项目启动脚本 (Windows)
REM 使用方式: start_celery.bat [worker|beat|flower|web|all|stop|status|help]

setlocal enabledelayedexpansion

REM 默认配置
set SETTINGS_MODULE=application.settings.zhi_celery
set LOG_LEVEL=info
set CONCURRENCY=2
set QUEUES=default,logger,oauth,system
set FLOWER_PORT=5555
set WEB_PORT=8004

REM 项目路径
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..\..
set BACKEND_PATH=%PROJECT_ROOT%\backend

REM 颜色定义 (Windows 10+)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

REM 函数：打印带颜色的消息
:print_message
echo %~2
goto :eof

REM 函数：检查依赖
:check_dependencies
echo %BLUE%🔍 检查系统依赖...%NC%

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Python未安装%NC%
    exit /b 1
)

REM 检查Redis连接
python -c "import redis; redis.Redis().ping()" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  Redis连接失败，请确保Redis服务正在运行%NC%
) else (
    echo %GREEN%✅ Redis连接正常%NC%
)

REM 检查必要的Python包
for %%p in (celery django redis) do (
    python -c "import %%p" >nul 2>&1
    if errorlevel 1 (
        echo %RED%❌ %%p 未安装%NC%
        exit /b 1
    ) else (
        echo %GREEN%✅ %%p%NC%
    )
)
goto :eof

REM 函数：启动Worker
:start_worker
echo %GREEN%🔄 启动Celery Worker...%NC%
cd /d "%BACKEND_PATH%"

python manage.py celery_worker ^
    --settings="%SETTINGS_MODULE%" ^
    --pool=solo ^
    --loglevel="%LOG_LEVEL%" ^
    --queues="%QUEUES%" ^
    --concurrency=%CONCURRENCY%
goto :eof

REM 函数：启动Beat
:start_beat
echo %GREEN%⏰ 启动Celery Beat...%NC%
cd /d "%BACKEND_PATH%"

python manage.py celery_beat ^
    --settings="%SETTINGS_MODULE%" ^
    --loglevel="%LOG_LEVEL%"
goto :eof

REM 函数：启动Flower
:start_flower
echo %GREEN%🌸 启动Flower监控...%NC%
cd /d "%BACKEND_PATH%"

set DJANGO_SETTINGS_MODULE=%SETTINGS_MODULE%
celery -A zhi_celery.celery_app flower ^
    --port=%FLOWER_PORT% ^
    --loglevel=%LOG_LEVEL%
goto :eof

REM 函数：启动Web服务
:start_web
echo %GREEN%🌐 启动ZhiCelery Web管理服务...%NC%
cd /d "%BACKEND_PATH%"

python ..\zhi_scripts\start_celery_service.py ^
    --port=%WEB_PORT% ^
    --settings=%SETTINGS_MODULE%
goto :eof

REM 函数：启动所有服务
:start_all
echo %GREEN%🚀 启动所有ZhiCelery服务...%NC%

REM 创建日志目录
if not exist "%BACKEND_PATH%\logs" mkdir "%BACKEND_PATH%\logs"

REM 启动Worker（后台）
echo %BLUE%启动Worker...%NC%
start "ZhiCelery Worker" /min cmd /c "cd /d \"%BACKEND_PATH%\" && python manage.py celery_worker --settings=\"%SETTINGS_MODULE%\" --pool=solo --loglevel=\"%LOG_LEVEL%\" --queues=\"%QUEUES%\" --concurrency=%CONCURRENCY% > logs\celery_worker.log 2>&1"

timeout /t 3 /nobreak >nul

REM 启动Beat（后台）
echo %BLUE%启动Beat...%NC%
start "ZhiCelery Beat" /min cmd /c "cd /d \"%BACKEND_PATH%\" && python manage.py celery_beat --settings=\"%SETTINGS_MODULE%\" --loglevel=\"%LOG_LEVEL%\" > logs\celery_beat.log 2>&1"

timeout /t 2 /nobreak >nul

REM 启动Flower（后台）
echo %BLUE%启动Flower...%NC%
start "ZhiCelery Flower" /min cmd /c "cd /d \"%BACKEND_PATH%\" && set DJANGO_SETTINGS_MODULE=%SETTINGS_MODULE% && celery -A zhi_celery.celery_app flower --port=%FLOWER_PORT% --loglevel=%LOG_LEVEL% > logs\celery_flower.log 2>&1"

timeout /t 2 /nobreak >nul

echo %GREEN%✅ 后台服务已启动:%NC%
echo %BLUE%   Flower监控: http://localhost:%FLOWER_PORT%%NC%
echo %BLUE%   日志目录: %BACKEND_PATH%\logs\%NC%

REM 启动Web服务（前台）
echo %GREEN%🌐 启动Web管理服务...%NC%
call :start_web
goto :eof

REM 函数：停止所有服务
:stop_all
echo %YELLOW%🛑 停止所有ZhiCelery服务...%NC%

REM 停止相关进程
taskkill /f /im python.exe /fi "WINDOWTITLE eq ZhiCelery*" >nul 2>&1
taskkill /f /im celery.exe >nul 2>&1

echo %GREEN%✅ 所有服务已停止%NC%
goto :eof

REM 函数：显示服务状态
:show_status
echo %BLUE%📊 ZhiCelery服务状态:%NC%

tasklist /fi "WINDOWTITLE eq ZhiCelery Worker" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Worker: 未运行%NC%
) else (
    echo %GREEN%✅ Worker: 运行中%NC%
)

tasklist /fi "WINDOWTITLE eq ZhiCelery Beat" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Beat: 未运行%NC%
) else (
    echo %GREEN%✅ Beat: 运行中%NC%
)

tasklist /fi "WINDOWTITLE eq ZhiCelery Flower" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Flower: 未运行%NC%
) else (
    echo %GREEN%✅ Flower: 运行中 (http://localhost:%FLOWER_PORT%)%NC%
)

REM 检查Web服务端口
netstat -an | find ":%WEB_PORT%" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Web服务: 未运行%NC%
) else (
    echo %GREEN%✅ Web服务: 运行中 (http://localhost:%WEB_PORT%)%NC%
)
goto :eof

REM 函数：显示帮助信息
:show_help
echo ZhiCelery 异步任务管理子项目启动脚本 (Windows)
echo.
echo 使用方式:
echo   %~nx0 [命令] [选项]
echo.
echo 命令:
echo   worker    启动Celery Worker
echo   beat      启动Celery Beat调度器
echo   flower    启动Flower监控
echo   web       启动Web管理服务
echo   all       启动所有服务
echo   stop      停止所有服务
echo   status    显示服务状态
echo   help      显示帮助信息
echo.
echo 环境变量:
echo   SETTINGS_MODULE   Django设置模块 (默认: %SETTINGS_MODULE%)
echo   LOG_LEVEL         日志级别 (默认: %LOG_LEVEL%)
echo   CONCURRENCY       Worker并发数 (默认: %CONCURRENCY%)
echo   QUEUES            队列名称 (默认: %QUEUES%)
echo   FLOWER_PORT       Flower端口 (默认: %FLOWER_PORT%)
echo   WEB_PORT          Web服务端口 (默认: %WEB_PORT%)
echo.
echo 示例:
echo   %~nx0 worker                    # 启动Worker
echo   %~nx0 all                       # 启动所有服务
echo   set CONCURRENCY=4 ^& %~nx0 worker      # 启动4个并发的Worker
echo   set LOG_LEVEL=debug ^& %~nx0 beat      # 以debug级别启动Beat
goto :eof

REM 主函数
:main
REM 显示启动信息
echo %BLUE%==================================================%NC%
echo %BLUE%🚀 ZhiCelery 异步任务管理子项目%NC%
echo %BLUE%==================================================%NC%
echo %BLUE%📁 项目路径: %PROJECT_ROOT%%NC%
echo %BLUE%⚙️  设置模块: %SETTINGS_MODULE%%NC%
echo %BLUE%📊 日志级别: %LOG_LEVEL%%NC%

REM 检查依赖
call :check_dependencies
if errorlevel 1 exit /b 1

REM 根据参数执行相应操作
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

if "%COMMAND%"=="worker" (
    call :start_worker
) else if "%COMMAND%"=="beat" (
    call :start_beat
) else if "%COMMAND%"=="flower" (
    call :start_flower
) else if "%COMMAND%"=="web" (
    call :start_web
) else if "%COMMAND%"=="all" (
    call :start_all
) else if "%COMMAND%"=="stop" (
    call :stop_all
) else if "%COMMAND%"=="status" (
    call :show_status
) else if "%COMMAND%"=="help" (
    call :show_help
) else if "%COMMAND%"=="--help" (
    call :show_help
) else if "%COMMAND%"=="-h" (
    call :show_help
) else (
    echo %RED%❌ 未知命令: %COMMAND%%NC%
    call :show_help
    exit /b 1
)

goto :eof

REM 执行主函数
call :main %*
