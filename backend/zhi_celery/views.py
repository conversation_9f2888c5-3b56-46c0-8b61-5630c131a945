"""
ZhiCelery 异步任务管理视图

前后端分离架构，主要提供简单的模板视图
API接口通过各个模型对应的API文件提供
"""

from django.shortcuts import render
from django.views.generic import TemplateView
from django.conf import settings

from .celery_app import app as celery_app
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)


class CeleryDashboardView(TemplateView):
    """Celery仪表板视图"""
    template_name = 'zhi_celery/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context.update({
            'title': 'ZhiCelery 异步任务管理',
            'api_docs_url': '/api/celery/docs/',
            'version': '1.0.0',
        })

        return context


class CeleryTasksView(TemplateView):
    """任务管理视图"""
    template_name = 'zhi_celery/tasks.html'


class CeleryWorkersView(TemplateView):
    """Worker管理视图"""
    template_name = 'zhi_celery/workers.html'


class CelerySchedulesView(TemplateView):
    """定时任务管理视图"""
    template_name = 'zhi_celery/schedules.html'


class CeleryMonitoringView(TemplateView):
    """监控视图"""
    template_name = 'zhi_celery/monitoring.html'


class CeleryDocsView(TemplateView):
    """API文档视图"""
    template_name = 'zhi_celery/docs.html'


