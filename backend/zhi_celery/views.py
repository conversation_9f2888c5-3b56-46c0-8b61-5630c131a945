"""
ZhiCelery 异步任务管理视图

提供Celery任务管理、监控和调度的Web界面和API
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.generic import TemplateView, View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

import json
from datetime import datetime, timedelta
from celery import current_app
from celery.result import AsyncResult
from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule

from .celery_app import app as celery_app
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)


class CeleryDashboardView(TemplateView):
    """Celery仪表板视图"""
    template_name = 'zhi_celery/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取Celery统计信息
        inspect = celery_app.control.inspect()

        context.update({
            'active_tasks': self._get_active_tasks_count(),
            'worker_count': self._get_worker_count(),
            'queue_count': len(getattr(settings, 'CELERY_TASK_QUEUES', [])),
            'scheduled_tasks': self._get_scheduled_tasks_count(),
            'failed_tasks': self._get_failed_tasks_count(),
            'success_rate': self._get_success_rate(),
        })

        return context

    def _get_active_tasks_count(self):
        """获取活跃任务数量"""
        try:
            inspect = celery_app.control.inspect()
            active = inspect.active()
            if active:
                return sum(len(tasks) for tasks in active.values())
            return 0
        except Exception as e:
            logger.error(f"获取活跃任务数量失败: {e}")
            return 0

    def _get_worker_count(self):
        """获取Worker数量"""
        try:
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            return len(stats) if stats else 0
        except Exception as e:
            logger.error(f"获取Worker数量失败: {e}")
            return 0

    def _get_scheduled_tasks_count(self):
        """获取定时任务数量"""
        try:
            return PeriodicTask.objects.filter(enabled=True).count()
        except Exception as e:
            logger.error(f"获取定时任务数量失败: {e}")
            return 0

    def _get_failed_tasks_count(self):
        """获取失败任务数量（最近24小时）"""
        # 这里需要根据您的结果存储后端实现
        # 如果使用django-celery-results，可以查询TaskResult模型
        return 0

    def _get_success_rate(self):
        """获取成功率"""
        # 这里需要根据您的结果存储后端实现
        return 95.5


class TaskListView(APIView):
    """任务列表API"""

    def get(self, request):
        """获取任务列表"""
        try:
            inspect = celery_app.control.inspect()

            # 获取活跃任务
            active_tasks = inspect.active() or {}

            # 获取预定任务
            scheduled_tasks = inspect.scheduled() or {}

            # 获取保留任务
            reserved_tasks = inspect.reserved() or {}

            tasks = []

            # 处理活跃任务
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['id'],
                        'name': task['name'],
                        'args': task['args'],
                        'kwargs': task['kwargs'],
                        'worker': worker,
                        'state': 'ACTIVE',
                        'timestamp': task.get('time_start'),
                    })

            # 处理预定任务
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['request']['id'],
                        'name': task['request']['task'],
                        'args': task['request']['args'],
                        'kwargs': task['request']['kwargs'],
                        'worker': worker,
                        'state': 'SCHEDULED',
                        'eta': task['eta'],
                    })

            return Response({
                'success': True,
                'data': {
                    'tasks': tasks,
                    'total': len(tasks),
                    'active_count': sum(len(tasks) for tasks in active_tasks.values()),
                    'scheduled_count': sum(len(tasks) for tasks in scheduled_tasks.values()),
                    'reserved_count': sum(len(tasks) for tasks in reserved_tasks.values()),
                }
            })

        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskDetailView(APIView):
    """任务详情API"""

    def get(self, request, task_id):
        """获取任务详情"""
        try:
            result = AsyncResult(task_id, app=celery_app)

            task_info = {
                'id': task_id,
                'state': result.state,
                'result': result.result,
                'traceback': result.traceback,
                'info': result.info,
                'successful': result.successful(),
                'failed': result.failed(),
                'ready': result.ready(),
            }

            # 如果任务还在运行，获取更多信息
            if not result.ready():
                inspect = celery_app.control.inspect()
                active_tasks = inspect.active() or {}

                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            task_info.update({
                                'worker': worker,
                                'name': task['name'],
                                'args': task['args'],
                                'kwargs': task['kwargs'],
                                'time_start': task.get('time_start'),
                            })
                            break

            return Response({
                'success': True,
                'data': task_info
            })

        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
