"""
ZhiCelery 异步任务管理视图

前后端分离架构，主要提供简单的模板视图
API接口通过 apis/celery_apis.py 提供
"""

from django.shortcuts import render
from django.views.generic import TemplateView
from django.conf import settings
from datetime import datetime, timedelta

from .celery_app import app as celery_app
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)


class CeleryDashboardView(TemplateView):
    """Celery仪表板视图"""
    template_name = 'zhi_celery/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取Celery统计信息
        inspect = celery_app.control.inspect()

        context.update({
            'active_tasks': self._get_active_tasks_count(),
            'worker_count': self._get_worker_count(),
            'queue_count': len(getattr(settings, 'CELERY_TASK_QUEUES', [])),
            'scheduled_tasks': self._get_scheduled_tasks_count(),
            'failed_tasks': self._get_failed_tasks_count(),
            'success_rate': self._get_success_rate(),
        })

        return context

    def _get_active_tasks_count(self):
        """获取活跃任务数量"""
        try:
            inspect = celery_app.control.inspect()
            active = inspect.active()
            if active:
                return sum(len(tasks) for tasks in active.values())
            return 0
        except Exception as e:
            logger.error(f"获取活跃任务数量失败: {e}")
            return 0

    def _get_worker_count(self):
        """获取Worker数量"""
        try:
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            return len(stats) if stats else 0
        except Exception as e:
            logger.error(f"获取Worker数量失败: {e}")
            return 0

    def _get_scheduled_tasks_count(self):
        """获取定时任务数量"""
        try:
            return PeriodicTask.objects.filter(enabled=True).count()
        except Exception as e:
            logger.error(f"获取定时任务数量失败: {e}")
            return 0

    def _get_failed_tasks_count(self):
        """获取失败任务数量（最近24小时）"""
        # 这里需要根据您的结果存储后端实现
        # 如果使用django-celery-results，可以查询TaskResult模型
        return 0

    def _get_success_rate(self):
        """获取成功率"""
        # 这里需要根据您的结果存储后端实现
        return 95.5


class TaskListView(APIView):
    """任务列表API"""

    def get(self, request):
        """获取任务列表"""
        try:
            inspect = celery_app.control.inspect()

            # 获取活跃任务
            active_tasks = inspect.active() or {}

            # 获取预定任务
            scheduled_tasks = inspect.scheduled() or {}

            # 获取保留任务
            reserved_tasks = inspect.reserved() or {}

            tasks = []

            # 处理活跃任务
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['id'],
                        'name': task['name'],
                        'args': task['args'],
                        'kwargs': task['kwargs'],
                        'worker': worker,
                        'state': 'ACTIVE',
                        'timestamp': task.get('time_start'),
                    })

            # 处理预定任务
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    tasks.append({
                        'id': task['request']['id'],
                        'name': task['request']['task'],
                        'args': task['request']['args'],
                        'kwargs': task['request']['kwargs'],
                        'worker': worker,
                        'state': 'SCHEDULED',
                        'eta': task['eta'],
                    })

            return Response({
                'success': True,
                'data': {
                    'tasks': tasks,
                    'total': len(tasks),
                    'active_count': sum(len(tasks) for tasks in active_tasks.values()),
                    'scheduled_count': sum(len(tasks) for tasks in scheduled_tasks.values()),
                    'reserved_count': sum(len(tasks) for tasks in reserved_tasks.values()),
                }
            })

        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskDetailView(APIView):
    """任务详情API"""

    def get(self, request, task_id):
        """获取任务详情"""
        try:
            result = AsyncResult(task_id, app=celery_app)

            task_info = {
                'id': task_id,
                'state': result.state,
                'result': result.result,
                'traceback': result.traceback,
                'info': result.info,
                'successful': result.successful(),
                'failed': result.failed(),
                'ready': result.ready(),
            }

            # 如果任务还在运行，获取更多信息
            if not result.ready():
                inspect = celery_app.control.inspect()
                active_tasks = inspect.active() or {}

                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            task_info.update({
                                'worker': worker,
                                'name': task['name'],
                                'args': task['args'],
                                'kwargs': task['kwargs'],
                                'time_start': task.get('time_start'),
                            })
                            break

            return Response({
                'success': True,
                'data': task_info
            })

        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskRetryView(APIView):
    """任务重试API"""

    def post(self, request, task_id):
        """重试任务"""
        try:
            result = AsyncResult(task_id, app=celery_app)

            if result.failed():
                # 重新执行任务
                new_result = result.retry()

                return Response({
                    'success': True,
                    'message': '任务重试成功',
                    'new_task_id': new_result.id
                })
            else:
                return Response({
                    'success': False,
                    'error': '只能重试失败的任务'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"任务重试失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskRevokeView(APIView):
    """任务撤销API"""

    def post(self, request, task_id):
        """撤销任务"""
        try:
            celery_app.control.revoke(task_id, terminate=True)

            return Response({
                'success': True,
                'message': '任务撤销成功'
            })

        except Exception as e:
            logger.error(f"任务撤销失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkerListView(APIView):
    """Worker列表API"""

    def get(self, request):
        """获取Worker列表"""
        try:
            inspect = celery_app.control.inspect()

            # 获取Worker统计信息
            stats = inspect.stats() or {}

            # 获取活跃Worker
            active = inspect.active() or {}

            workers = []
            for worker_name, worker_stats in stats.items():
                worker_info = {
                    'name': worker_name,
                    'status': 'online' if worker_name in active else 'offline',
                    'active_tasks': len(active.get(worker_name, [])),
                    'processed_tasks': worker_stats.get('total', {}).get('tasks.processed', 0),
                    'pool': worker_stats.get('pool', {}).get('implementation', 'unknown'),
                    'concurrency': worker_stats.get('pool', {}).get('max-concurrency', 0),
                    'load_avg': worker_stats.get('rusage', {}).get('utime', 0),
                }
                workers.append(worker_info)

            return Response({
                'success': True,
                'data': {
                    'workers': workers,
                    'total': len(workers),
                    'online': len([w for w in workers if w['status'] == 'online']),
                    'offline': len([w for w in workers if w['status'] == 'offline']),
                }
            })

        except Exception as e:
            logger.error(f"获取Worker列表失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkerStatsView(APIView):
    """Worker统计API"""

    def get(self, request, worker_name):
        """获取Worker详细统计"""
        try:
            inspect = celery_app.control.inspect([worker_name])

            stats = inspect.stats() or {}
            active = inspect.active() or {}
            reserved = inspect.reserved() or {}

            worker_stats = stats.get(worker_name, {})

            return Response({
                'success': True,
                'data': {
                    'name': worker_name,
                    'stats': worker_stats,
                    'active_tasks': active.get(worker_name, []),
                    'reserved_tasks': reserved.get(worker_name, []),
                    'active_count': len(active.get(worker_name, [])),
                    'reserved_count': len(reserved.get(worker_name, [])),
                }
            })

        except Exception as e:
            logger.error(f"获取Worker统计失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class QueueListView(APIView):
    """队列列表API"""

    def get(self, request):
        """获取队列列表"""
        try:
            # 从配置中获取队列信息
            queues = getattr(settings, 'CELERY_TASK_QUEUES', [])

            queue_info = []
            for queue in queues:
                queue_name = queue.name if hasattr(queue, 'name') else str(queue)

                # 这里可以添加更多队列统计信息
                # 需要根据您的消息代理（RabbitMQ/Redis）实现
                queue_info.append({
                    'name': queue_name,
                    'routing_key': getattr(queue, 'routing_key', queue_name),
                    'messages': 0,  # 需要从消息代理获取
                    'consumers': 0,  # 需要从消息代理获取
                })

            return Response({
                'success': True,
                'data': {
                    'queues': queue_info,
                    'total': len(queue_info),
                }
            })

        except Exception as e:
            logger.error(f"获取队列列表失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class QueueStatsView(APIView):
    """队列统计API"""

    def get(self, request, queue_name):
        """获取队列详细统计"""
        try:
            # 这里需要根据您的消息代理实现具体的队列统计
            # 以下是示例结构

            return Response({
                'success': True,
                'data': {
                    'name': queue_name,
                    'messages': 0,
                    'consumers': 0,
                    'message_rate': 0.0,
                    'consumer_rate': 0.0,
                }
            })

        except Exception as e:
            logger.error(f"获取队列统计失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
