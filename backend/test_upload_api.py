#!/usr/bin/env python3
"""
测试文件上传API的脚本
"""
import requests
import os

def test_file_upload():
    """测试文件上传功能"""
    
    # API配置
    url = "http://localhost:8000/api/enhanced-files/upload"
    token = "access-8f137cefc3fc034f3f25215ac7c9b8935a45d5959bf600b468"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 准备测试文件
    test_file_path = "test_upload.txt"
    
    if not os.path.exists(test_file_path):
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件，用于测试文件上传功能。\nTest file for upload functionality.\n测试时间：2025-08-04")
    
    try:
        # 发送文件上传请求
        with open(test_file_path, 'rb') as f:
            files = {
                'file': ('test_upload.txt', f, 'text/plain')
            }
            data = {
                'description': '测试文件上传',
                'tags': 'test,upload'
            }
            
            print("正在发送文件上传请求...")
            response = requests.post(url, headers=headers, files=files, data=data)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ 文件上传成功！")
                return True
            else:
                print("❌ 文件上传失败")
                return False
                
    except Exception as e:
        print(f"❌ 请求发送失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_file_upload()
